#pragma once
#include <stdlib.h>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include "data/jce/user_data.h"
#include "public/ai_profile_control/ai_profile_control_utils.h"
#include "utils/xxhash.h"

namespace ai {
namespace minister {
#define ERROR_OR_INFO_LOG(condition, err_msg, ...) \
  {                                                \
    if (condition) {                               \
      INFO_LOG(err_msg, ##__VA_ARGS__);            \
    } else {                                       \
      ERROR_LOG(err_msg, ##__VA_ARGS__);           \
    }                                              \
  }
#define xxhash(x) XXH64(x.c_str(), x.size(), 8)
#define random_int(x) rand() % (x)
#define random_double rand() / double(RAND_MAX)
#define sigmoid(x) 1.0 / (1 + std::exp(-1.0 * x))
#define truncate_float(x, y) (float)((float)((int)(x / y)) * y)
template <class key, class value>
bool pair_sort(const std::pair<key, value> &A, const std::pair<key, value> &B) {
  return A.second > B.second;
};

/*
    recall_num: 通路最大返回数量
    seek_num: 通路最大检索数量
    recall_time: 通路最大检索耗时
    zigzag_param: zigzag填充参数 Scene代表场景 Layer代表通路类型 L0生态层 L1策略层 L2模型层
    Scene定义: kZigzagSceneMap
    Layer定义: kZigZagLayerVector
    pathid: 通路id设置
*/
#define define_recall_para(name, recall_num, seek_num, recall_time, zigzag_param, path_id) \
  static const std::string k_recall_##name##_name = #name;                                 \
  static const int k_recall_##name##_rc_num = recall_num;                                  \
  static const int k_recall_##name##_sk_num = seek_num;                                    \
  static const int k_recall_##name##_max_t = recall_time;                                  \
  static const std::string k_recall_##name##_zg_prm = zigzag_param;                        \
  static const int k_recall_##name##_path_id = path_id;
#define define_model_para(name) static const std::string k_model_##name##_name = #name;
#define define_rpc_para(name) static const std::string k_rpc_##name##_name = #name;
#define define_feature(x) static const std::string k_##x##_fea = #x
#define define_counter(x) static const std::string k_##x##_counter = #x
static const std::string kStrUnknow = "Latest";
static const std::string kStrSongsLiked = "liked_songs";
static const std::string kStrSingersLiked = "liked_singers";
static const std::string kStrAgeLiked = "liked_ages";
static const std::string kStrTypeLiked = "liked_types";
static const std::string kStrSongsHot = "hot_songs";
static const std::string kStrDeepSongs = "deep_songs";
static const std::string kStrDailyDeepSongs = "daily_deep_songs";
static const std::string kStrMinisterDeepSongs = "minister_deep_songs";
static const std::string kStrFavDeepSongs = "fav_deep_songs";
static const std::string kStrNewUserSongs = "new_user_songs";
static const std::string kStrU2USongs = "u2u_songs";
static const std::string kStrNewSongsInsert = "newsong@insert";
static const std::string kStrFriendSongs = "friend_songs";
static const std::string kStrCrossDeepSongs = "cross_deep_songs";
static const std::string kStrPdnDeepSongs = "pdn_deep_songs";
static const std::string kStrReturnUserSongs = "return_user_songs";
static const std::string kStrFavoSongs = "favo_songs"; // 收藏歌曲
static const std::string kStrRecentSongs = "recent_songs"; // 近期常听但没收藏
static const std::string kStrVerticalRecSongs = "vertical_rec_songs"; // 垂类推荐歌曲

// 由于歌曲歌手模块前三首有语种不一样的，就换替换文案
static const std::string kStrReplaced = "replace";

// 单曲服务本地服务配置
static const int kMinisterTimeout = 690;
static const int kMinisterPort = 17173;
static const int kMinisterNoReturnErr = -17173;
static const int KMinisterMid = 205365919;
static const int KministerIfc = 180009281;

static const int kPFBid = 270;
static const int kPFTrackBatchSize = 50;
static const int kPFGetSongBySingerTimeout = 60;
static const int kCacheTrackNum = 200000;
static const int kCahceTrackOutofSecs = 18000;
static const int kCacheAlbumNum = 100000;
static const int kCahceAlbumOutofSecs = 180000;
static const int kCacheStrackNum = 1000000;
static const int kCahceStrackOutofSecs = 10800;
static const int kACTrackBatchSize = 25;
static const int kACRequestTimeoutMs = 120;
static const int kMusicEyeFeatureBatchSize = 200;
static const int kCacheMusicEyeFeatureNum = 500000;
static const int kCahceMusicEyeFeatureOutofSecs = 36000;
static const int kCacheMusicEyeScoreNum = 1000000;
static const int kCahceMusicEyeScoreOutofSecs = 10800;
static const int kMusicEyeBatchSize = 20;
static const int kCacheMusicEyeLabelNum = 300000;
static const int kCahceMusicEyeLabelOutofSecs = 18000;
static const int kCacheContentPoolOutofSecs = 7200;
static const uint32_t kACRequestBatchSize = 24;

// 自定义画像衰减
static const float kProfileDecayRate = 0.95;
static const float kProfileFavoDecayRate = 0.99;
static const float kProfileSelfDecayRate = 0.99;
static const float kProfileListDecayRate = 0.95;
static const float kProfileDownDecayRate = 0.95;
static const float kProfileSingDecayRate = 0.95;
static const float kQueryDecayRate = 0.999;
static const float kProfileMinScore = 0.8;
static const float kProfileSongMinScore = 0.05;
static const float kProfileDeviceMinScore = 0.3;
static const float kProfileOuterMinScore = 0.3;
static const float kProfileTimeMs = 100;

// 自定义画像行为基础分
static const float kActionBaseScore = 1.0;
static const float kActionFavoScore = 1.0;
static const float kActionSelfScore = 0.9;
static const float kActionListScore = 0.5;
static const float kActionDownScore = 0.5;
static const float kActionSingScore = 0.5;
static const float kActionQueryScore = 1.0;
static const float kActionFavoMinScore = 0.01;
static const float kActionSelfMinScore = 0.01;
static const float kActionListMinScore = 0.001;

// 旧资产服务相关
static const int kAssetLongAudioType = 7;   // 长音频
static const int kAssetLongAccomType = 8;   // 伴奏
static const int kAssetRecoListenType = 4;  // 用户资产听歌推荐Tab渠道

// 资产融合控制参数
static const int kAssetLightUser = 5;  // 低资产用户定义（down+comp+fav<=5）

// lifelong 资产参数
static const int kLifeLongSeqTruncateNum = 10000;
static const int kCancelFavSeqTruncateNum = 500;
static const int kLifeLongSeqSampleTruncateNum = 1000;
static const int kLifeLongSeqTakeNum = 100;
static const int kLifeLong30dTop5SeqTakeNum = 5;

// 透传服务请求type
static const int kQmSessionProxyType = 0;  // q音主站 默认全部获取

// 星画获取人群标识
static const string LowerPeopleRule = "10000716";       // 下沉人群
static const string RecLowLivePeopleRule = "10000284";  // 底活人群

// 拉黑相关控制
static const int kFilterBlackSongSingerExpire = 30 * 3600 * 24;  // 过滤拉黑歌曲的歌手（非熟悉）所有歌曲，生效时间：拉黑后60日
static const int kFilterBlackSongSingerLastN = 7;                // 过滤拉黑歌曲的歌手，最近10条记录
static const int kFilterBlackSongSingerExpireShort = 10 * 3600 * 24;  // 过滤拉黑歌曲的歌手（熟悉）所有歌曲，生效时间：拉黑后10日内
static const int kFilterBlackSongSingerMaxFreq = 2;                   // 同一个歌手被拉黑超过2首歌曲，永久直接拉黑
static const int kFilterBlackSongLanguageExpire = 7 * 3600 * 24;  // 过滤小语种拉黑歌曲的对应语种，生效时间：拉黑后7日

// 新实验平台
static const int kExpPlatChanId = 122;
static const int kExpPlatBussId = 10;
static const std::vector<std::string> kExpPlatModules = {
    "MinisterBusiness1", "MinisterBusiness2", "MinisterBusiness3", "MinisterBusiness4", "MinisterBusiness5", "MinisterRecall", "MinisterRecall1",
    "MinisterRecall2", "MinisterRecall7", "MinisterRecall9", "MinisterCrossRecall", "MinisterRoughSort", "MinisterSort", "MinisterLongtailReSort",
    "recommend20220527", "MinisterRecallFei", "MinisterRecallFeiDSSMSong", "MinisterRecallFeiDSSMSinger", "MinisterSource", "MinisterSourceStrategy",
    "MinisterContent", "MinisterReSort1", "MinisterReSortStrategy", "MinisterDeliveryStrategy",
    //  "MinisterLog",
    "MinisterRecallFeiPinnerFormer", "MetaDataSvr", "MinisterRecallQK", "UnifiedRecallKGU2IStar", "MinisterCrossDssmUltra",
    "MinisterLongSearchInterface", "MinisterUnifyU2U2I", "MinisterDapanRecallPerm", "MinisterFeature", "MinisterMusicEyeFeature",
    "MinisterLongtailRoughQuota", "UnifiedRecallKGU2ISasrec", "MinisterRecallFeiDSSMSongCold", "MinisterRecallFeiDSSMSingerCold",
    "MinisterRecallCold", "MinisterHuiyuanSort", "MinisterDelaySort", "MinisterRecallTerasearch", "MinisterRecallGraph", "MinisterRedisLock",
    "RecTaggerRecall", "MinisterRecallAblation", "MinisterRecallAblationNew", "MinisterDeliver", "MinisterRecallMove", "minisiter_Card",
    "MinisterNewSong", "MinisterFavorTargetDeliveryRecall", "MinisterTransfer", "MinisterShowNew", "MinisterContentVisibility", "MinisterTDMClose",
    "MinisterFrameWork", "MinisterFrameWork2", "MinisterUnifiedSinger",
    //"MinisterLLM",
    "MinisterLongtailFlow", "MinisterHourlyBrush", "MinisterFilter2", "MinisterFilter3", "MinisterNewsongUplift", "MinisterDiffuRec",

    "MinisterFrameWork3", "MinisterCodeOpt", "RecTabRealtime"};

// MusicEye参数
static const std::string kMESearchFieldSongID = "metadata.songid";
static const std::string kMESearchFieldGenreID = "metadata.genre_id";
static const std::string kMESearchFieldHotScore = "params.hot_score";
static const int kMESearchByGenrePMID = 13;

// FA7索引参数
static const std::string kFA7Index = "song_high_quality";
static const std::string kFA7FieldFTime = "ftime";
static const std::string kFA7FieldSongID = "songid";
static const std::string kFA7FieldSingerID = "fsinger_id1";
static const std::string kFA7FieldAlbumID = "falbum_id";
static const std::string kFA7FieldLanguage = "flanguage";
static const std::string kFA7FieldDuration = "fduration";
static const std::string kFA7FieldPoolID = "pool_id_list";
static const std::string kFA7FieldNewTagList = "new_tag_list";
static const std::string kFA7FieldRuleIDList = "rule_id_list";
static const std::string kFA7FieldSingerGrade = "fsinger_grade";
static const std::string kFA7OperationTerm = "term";
static const std::string kFA7OperationAnd = "and";
static const std::string kFA7OperationOr = "or";
static const std::string kFA7OperationPK = "pk";
static const std::string kFA7HookAttrRank = "attr_ranker";
static const std::string kFA7FieldEagleScore = "qm_eagle_final_score";
static const std::string kFA7NewPoolID = "106#1";
static const std::string kRecallFA7SingerNewHourPoolID = "104";
static const std::string kRecallFA7SingerNewPlusDayPoolID = "1347";
static const uint32_t kRecallFA7MaxSingerNumForNew = 20;
static const std::string kFA7VipPoolID = "112@1";
static const std::string kFA7ColdHotPoolID = "109#1";
static const std::string kFA7ColdPremPoolID = "109#2";
static const std::string kFa7PoolNewUser = "109@3";
static const int kFA7ModID = 1881473;
static const int kFA7CmdID = 13762560;
static const int kFA7ModuleID = 205366480;
static const int kFA7GenreModID = 2066497;
static const int kFA7GenreCmdID = 131072;
static const int kFA7GenreModuleID = 205366480;

static const std::unordered_map<std::string, std::string> kGenreFA7PoolMap = {
    {"4_15", "101#4_15"},   {"4_119", "101#4_119"}, {"5_15", "101#5_15"},     {"5_119", "101#5_119"}, {"5_98", "101#5_98"},   {"5_282", "101#5_282"},
    {"5_352", "101#5_352"}, {"0_15", "101#0_15"},   {"0_1134", "101#0_1134"}, {"0_119", "101#0_119"}, {"0_352", "101#0_352"}, {"1_15", "101#1_15"},
    {"0_282", "101#0_282"}, {"0_150", "101#0_150"}, {"1964", "101#1964"},     {"9_576", "101#9_576"}, {"29", "101#29"},       {"2194", "101#2194"},
    {"2198", "101#2198"},   {"3_15", "101#3_15"},   {"3_1134", "101#3_1134"}, {"3_119", "101#3_119"},
};

// 统一特征转发服务接口参数
static const uint64_t kFeatureUnifiedModId = 1838785;
static const uint64_t kFeatureUnifiedCmdId = 262144;

// 流派替换ID
static const std::string kGenreReplaceOld = "1143";
static const std::string kGenreReplaceDJ = "1142";
static const std::string kGenreReplaceNew = "2194";
static const std::string kGenreGetLast = "0_15";

static const int kBidStyle = 40;
static const int kBidContent = 240;
static const int kBidRoughContent = 2401;
static const int kBidSongVip = 4;
static const int kBidSwing = 131;
static const std::string kBidAsset = "15516";
static const std::string kBidUuidAsset = "88";

// 记录下发历史参数控制
static const int kRecoMaxHistoryNum = 700;
static const int kRecoMaxLatestHistoryNum = 300;
static const int kRecoMaxHistoryDetailNum = 90;
static const int kRecoMaxHistoryDetailForDebugNum = 210;
static const int kRecoMaxHistoryMonth = 3;
static const int kRecoMaxSongNum = 5;
static const int kRecoMaxSingerNum = 5;
static const int kActionRequestTimeoutMs = 80;
static const int kActionMaxDays = 90;           // 只取90天内行为
static const int kActionMaxDaysForOthers = 30;  // 其他模块的曝光只取30天
static const float kActionDecayRate = 0.99;
static const float kActionMinComplete = 0.80;

// 获取用户去重历史时间控制
static const int kDupCutListenMaxDays = 10;
static const int kDupCutListenExploreMaxDays = 30;
static const int kDupAiRadioDustMaxDays = 30;
static const int kDupFavoriteMaxDays = 180;
static const int kDupFavoriteMaxLen = 0;
static const int kDupSelfMaxDays = 30;
static const int kDupListenMaxDays = 30;
static const int kDupListenExploreMaxDays = 90;
static const int kDupDownMaxDays = 60;
static const int kDupFavoriteExploreMaxDays = 360;
static const int kDupFavoriteExploreMaxLen = 500;
static const int kDupSongNameListenMaxDays = 7;
static const int kDupSongNameListenMaxLen = 100;
static const int kDupRecoHourTime = 24;
static const int kDupRecoSourceSongNum = 0;
static const int kDupRecoSourceSingerNum = 0;

// 星画资产召回控制参数-目前低资产用户专用
static const int kXHAssetModuleID = *********;
static const int kXHAssetInterfaceID = *********;
static const string kXHAssetCallerKey = "";
static const std::vector<string> kXHAssetApps = {userdata::E_USERDATA_APP_KGO_XH, userdata::E_USERDATA_APP_KW_XH};
static const std::vector<string> kXHAssetKeys = {userdata::E_USERDATA_ACTION_TYPE_DOWNLOAD_XH, userdata::E_USERDATA_ACTION_TYPE_FAV_XH,
                                                 userdata::E_USERDATA_ACTION_TYPE_FINISH_XH};

// TDBank上报
static const std::string kTDBankBusinessId = "b_sng_qqmusic_qqmusic_song_rec_feature";
static const std::string kTDBankTableId = "ai_song_reco_feature_minister";
// 未下发采样上报
static const std::string kTDBankConsistencySampleTableId = "ai_song_consistency_sample_feature_minister";
// 未下发采样比例(采样数 / 下发数)
static const int kConsistencySampleRatio = 1;
// 未下发采样的起始位置
static const int kConsistencySampleStartPosition = 100;
static const int kConsistencySampleLastPosition = 200;
// 全链路trace上报
static const std::string kTDBankFullTraceId = "ai_reco_full_trace_minister";
static const float kFullTraceSampleRatio = 0.01;

// 源排序参数控制
const static std::string kSourceSongModelName = "minister_source_song_rkv1";
const static std::string kSourceSingerModelName = "minister_source_singer";
const static int kNumQuitSingerDay = 90;

// 粗排参数控制
const static std::string kRoughModelName = "minister_distill_nce_v2";
const static std::string kDistillRoughModelName = "minister_distill_nce_v2";
const static std::string kTDMRoughModelName = "minister_tdm_v1";
const static std::string kWDRoughModelName = "wordvec";
static const int kNumTrpcTimeOut = 100;
static const int kNumRoughBatchSize = 1;
static const int kNumSourceEmbDim = 32;
static const int kNumRoughEmbDim = 64;
static const uint32_t kPredictPlaceholderID = 0;
// 粗排重复模型
const static std::string kRoughDupName = "minister_rough_dup";
// 精排参数控制
static const std::string kFRMusicEyeSortIndexName = "song_all@all";
static const std::string kFRMusicEyeSortKey = "params.offline_combined_score";
static const float kFRDefaultScore = 0.000000001;
const static std::string kTFModelNameV7 = "minister_v8";
const static std::string kTFPredictName1 = "Sigmoid_1";  // wide
const static std::string kTFPredictName2 = "Sigmoid_2";  // dnn
const static std::string kTFPredictName3 = "Sigmoid_3";
static const std::string kTFPredictModelName = "song_mtl_v1";
static const std::string kTFPredictMulTargetNameClick = "click";
static const std::string kTFPredictMulTargetNameComplete = "complete";
static const std::string kTFPredictMulTargetNameLike = "like";
static const std::string kTFPredictMulTargetNameCut = "cut";
static const std::string kTFPredictMulTargetNameLongPlay = "long_play";
static const std::string kTFPredictMulTargetNameTask1 = "task1_pre";
static const std::string kTFPredictMulTargetNameTask2 = "task2_pre";
static const std::string kTFPredictMulTargetNameTask3 = "task3_pre";

// 重排参数控制
static const std::string kReSortMulTargetNameClick = "pred_click";
static const std::string kReSortMulTargetNameComplete = "pred_complete";
static const std::string kReSortMulTargetNameFavor = "pred_favor";
static const std::string kReSortMulTargetNameLongPlay = "pred_long_play";
static const std::string kReSortMulTargetNameCut = "pred_cut";
const static int KReSortNum = 50;
const static int kReSortSingerMaxNum = 2;

const static int kTFModelV1ModId = 1977665;
const static int kTFModelV1CmdId = 196609;
const static int kTFModelV1TimeOut = 120;
const static int kTFModelV1ModuleId = 205366261;
const static int kTFModelV2ModId = 2182913;
const static int kTFModelV2CmdId = 65536;
const static int kTFModelV2TimeOut = 120;
const static int kTFModelV2ModuleId = 205366422;
const static int kTFBatchSize = 75;
// 机制参数控制
static const int kMRSingerNumPerRequest = 1;
static const int kMRAblumNumPerRequest = 1;
static const int kMRSongNumPerRequest = 1;
static const int kMRSameSongPerRequest = 1;
static const int kMRAlbumSingerNumPerRequest = 3;
static const int kMRIgnoreDiversitySingerNumPerRequest = 2;
static const int kMRIgnoreDiversityAblumNumPerRequest = 2;
static const int kMRIgnoreDiversityAblumSingerNumPerRequest = 2;
static const int kMRItemMaxNumPerRequest = 30;
static const int kMR13ItemMaxNumPerRequest = 32;
static const int kMR13NotVipItemMaxNumPerRequest = 33;
static const int kMRLiteAifmItemNumPerRequest = 5;
static const int kMRItemCompareNumPerRequest = 15;
static const int kMRItemScoreNumPerModule = 24;
static const int kMRItemFreePopNumPerRequest = 10;
static const int kMRItemMinNumPerRequest = 6;
static const int kMR13ItemMinNumPerRequest = 4;
static const int kMRPlayTabItemMinNumPerRequest = 3;
static const int kMRVipItemMinNumPerRequest = 30;
static const int kMRItemNumPerPage = 3;
static const int kMR13ItemNumPerPage = 4;
static const float kMRMinScoreForDiversity = 2.0;
static const int kMRIgnoreDiversitySingerNum = 1;
static const int kMaxFavNum = 6;
static const int kMaxFavPerPageNum = 1;
static const int kMaxCoverNum = 3;
static const int kMaxCoverPerPageNum = 1;
static const int kMaxRoughSingerNum = 2;
static const int kMaxRoughSongNameNum = 2;
static const int kFinalFavPageNum = 2;
static const int kMaxRecallSingerNum = 3;

// 图谱作词作曲召回参数
static const string kKGRecallSongIdentifier = "recallTrackByTrack";
static const string kKGRecallSingerIdentifier = "recallTrackByAuthor";
static const string kKGTrackIDS = "track_ids";
static const string kKGAuthorIDS = "author_ids";
static const string kKGAuthorType = "author_type";
static const string kKGAuthorTypeCompose = "0";  // 0-查询同作词的歌曲
static const string kKGAuthorTypeLyrics = "1";   // 1-查询同作曲的歌曲
static const string kKGNumKey = "nums";
static const string kKGNum = "100";          // 限制返回的同作词/同作曲 歌曲数量
static const int kKGSongStrategyID = 102;    // 根据歌曲进行同作词作曲召回的策略id
static const int kKGSingerStrategyID = 103;  // 根据歌手进行同作词作曲召回的策略id

// 新歌提权
static const int kMRNewSongMultNum = 6;
static const int kMRNewSongMultTopNum = 1;
static const float kMRNewSongMultScore = 5.0;
static const float kMRNewSongMultTopScore = 10.0;
static const int kMRNewSongTopDay = 21;
static const int kMRNewLatestSongTopDay = 7;
static const int kRecallFilterNewItemNum = 90;
static const int kRecallFilterNewAlbumNum = 2;  // 新歌走歌手类召回的同一专辑 最多曝光3次

// vip30首封面提权
static const int kMRVipCoverDay = 5 * 365;
static const int kMRVipCoverDayNum = 5;

// 投放参数控制
static const int KTFMaxNumPerRequest = 2;  // 每次请求最多下发2个
static const int kTFDefaultPage = 2;       // 默认投放位置右滑第二屏
static const int kTFChannelIDSong = 342;
static const int kTFChannelIDSinger = 255;
static const int kTFChannelIDUnified = 253;
static const int kTFChannelIDVipLimited = 383;
static const int kTFChannelIDSimTrigger = 384;

// 投放收藏计费任务的渠道ID
static const int kTFChannelIDFavorTargetSong = 381; 
static const int kTFChannelIDFavorTargetSinger = 380;
static const int kTFChannelIDFavorTargetUnified = 378;
static const int kTFChannelIDFavorTargetVipLimited = 385;
static const int kTFChannelIDFavorTargetSimTrigger = 386;

// 飙升控速的渠道ID
static const int kTFChannelIDSpeedControl = 397;

static const int kTFContentType = 1;
static const int kTFRequestTimeMs = 60;
static const int kTFReportTimeMs = 30;

// 模块触发参数
static const int kModule30ListenNumActiveLevelMid = 30;
static const int kModule30ListenNumActiveLevelHigh = 100;
static const int kModuleMaxNumFirst = 2;
static const int kModuleMaxNumSecond = 5;

// 源排序参数
static const int kSourceRandomNum = 20;

// 召回参数控制
static const int kRecallTimeMs = 150;                         // 单条召回的超时控制
static const int kRecallUnifiedThreshold = 5000;              // 统一召回最大距离阈值
static const int kRecallUnifiedNormalThreshold = 1;           // 统一召回最大距离阈值
static const int kRecallUnifiedAudioThreshold = 4;            // 统一召回最大距离阈值
static const int kRecallUnifiedAdvanceThreshold = 100;        // 统一召回最大距离阈值
static const float kRecallUnifiedAdvanceNormalThreshold = 1;  // 统一召回最大距离阈值
static const int kRecallGenres = 4;                           // 依此召回的流派候选数目限制
static const int kRecallSongs = 50;                           // 依此召回的I2I歌曲数目限制
static const int kRecallVipSongs = 100;                       // 依此召回的I2I歌曲数目限制
static const int kRecallNewSongs = 20;                        // 依此召回的I2I歌曲数目限制
static const int kRecallSingers = 40;                         // 依此召回的I2I歌曲数目限制
static const int kRecallSongsPerSingers = 5;                  // 依此召回的I2I歌曲每个歌手最大歌曲数目限制
static const int kRecallTopSingers = 10;                      // 依此召回的歌手数目限制
static const int kRecallTopSongs = 20;                        // 依此召回的歌曲数目限制
static const int kRecallSingerSongs = 20;                     // 依此召回的歌手的歌曲数目限制
static const int kRecallIPSongs = 50;                         // 依此召回的ip的歌曲数目限制
static const int kRecallUnifiedGenreSongIdSeedNum = 40;       // 统一召回根据流派召回每个流派分配的歌曲数目
static const int kRecallNewSongTopFollowers = 20;             // 依此召回最多N个关注歌手的新歌
static const int kRecallNewSongTopFA7Followers = 40;          // 依此召回最多N个关注歌手的新歌
static const float kRecallNewSongMinSingerScore = 3.0;        // 依此召回画像分大于x的歌手的新歌
static const int kRecallVipMaxSingerNum = 50;                 // 依此召回最多N个歌手的VIP歌曲
static const float kRecallVipMinSingerScore = 1.0;            // 依此召回歌手画像分最小门槛
static const float kRecallHotSongMinSingerScore = 3.0;        // 依此召回画像分大于x的歌手的新歌
static const int kRecallFilteredDedupMinSongProfileNum = 15;
static const int kRecallFilteredDedupMinSingerProfileNum = 10;
static const int kRecallIPLevel = 1;  // 默认ip召回第一层级的结果
static const int kRecallI2IKNN = 400;
static const int kSourceSingerNum = 10;
static const std::string kPoolAllSourceIndex = "song_nometa_index.alias";
static const std::string kPoolMusicianSourceIndex = "song_audio";
static const std::string kPoolWhiteSourceIndex = "recwhite_song_vec_nometa.index.alias";
static const std::string kPoolAdvanceWhiteSourceIndex = "song_high_recwhite_pool_nonorm.alias";
static const std::string kPoolPremiumSourceIndex = "premium_song.index.alias";
static const std::string kPoolAllNormSourceIndex = "song_vec_index_norm.alias";
static const std::string kPoolWhiteNormSourceIndex = "recwhite_song_vec_norm.index.alias";
static const std::string kPoolWhiteNormPoolidSourceIndex = "recwhite_song_vec_norm_with_pool_ids.alias";
static const std::string kPoolQKSeqSourceIndex = "qk_seq_song_sasrec_i2i_source.alias";
static const std::string kPoolQKSeqTargetIndex = "qk_seq_song_sasrec_low.alias";
static const std::string kPoolKGGloveSourceIndex = "song_recall_glove.alias";
static const std::string kPoolKGGloveTargetIndex = "song_recall_glove_white.alias";
static const std::string kPoolAdvanceWhiteNormSourceIndex = "song_high_recwhite_pool.alias";
static const std::string kPoolContentSourceIndex = "song_content_pool_all.alias";
static const std::string kContentPoolHotNew = "453";
static const std::string kContentPoolClean = "467";
static const std::string kDSSMEfIndex = "song_recall_dssm_knn.alias";
static const std::string kDSSMModelName = "user_embedding_norm_knn";
static const std::string kDSSMSongColdEfIndex = "minister_dssm_song_v4_longtail.alias";
static const std::string kDSSMSongEfIndex = "minister_dssm_song_v4.alias";
static const std::string kDSSMSongModelName = "user_vec_norm_song_v4";
static const std::string kDSSMESingerfIndex = "minister_dssm_singer_v4.alias";
static const std::string kDSSMSingerModelName = "user_vec_norm_singer_v4";
static const std::string kQKSeqEfIndex = "qk_seq_song_sasrec_low.alias";
static const std::string kQKSeqModelName = "qk_seq_user_embedding_star";
static const std::string kDSimSingerIndex = "playlist_kg_sagessl_singerv1_singer.alias";
static const std::string kDSimSingerSongIndex = "playlist_kg_sagessl_singerv1_track_rec.alias";
static const std::string kDSimSingerSongSrcIndex = "playlist_kg_sagessl_singerv1_track.alias";
static const std::string kGNNSourceIndex = "ple_gnn_song_user.alias";
static const std::string kGNNTargetIndex = "ple_gnn_song_item.alias";
static const std::string kPinnerFormerIndex = "song_recall_pinner_former.alias";
static const std::string kPinnerFormerModelName = "pinner_user_embedding";
static const std::string kComirecModelName = "comirecSA_nce_fav_l2";
static const std::string kComirecServeName = "trpc.Serving.QMSongRecallCrossComirecServerNM.PredictInterfaceObj";
static const std::string kComirecUserVectorName = "user_cross_comirec_embedding";
static const std::string kComirecIndex = "song_cross_comirec_recall.alias";
static const std::string kPdnServerName = "trpc.Serving.MinisterSongSourceV1ServerNM.PredictInterfaceObj";
static const std::string kPdnUserVectorName = "user_engage_click_vector";
static const std::string kPdnSongItemIndex = "minister_source_v2";
static const std::string kPdnSongSourceIndex = "minister_source_v1";
static const std::string kPdnSingerItemIndex = "minister_source_v4";
static const std::string kPdnSingerSourceIndex = "minister_source_v3";
static const std::string kSasrecServeName = "trpc.Serving.SongRecallCrossSasrecMVKEServerNM.PredictInterfaceObj";
static const std::string kSasrecUserVectorName = "user_cross_sasrec_mvke_embedding";
static const std::string kSasrecIndex = "song_cross_sasrec_mvke_recall.alias";
static const std::string kDistillServeName = "trpc.Serving.MinisterQmDistillRoughV2ServerNM.PredictInterfaceObj";
static const std::string kDistillCompUserVectorName = "user_engage_comp_vector_rough";
static const std::string kDistillCompIndex = "rough_gpu_play_minister_item_vector";
static const std::string kDistillLongPlayUserVectorName = "user_engage_long_play_vector_rough";
static const std::string kDistillLongPlayIndex = "rough_gpu_fav_minister_item_vector";
static const std::string kSimI2IIndex = "minister_rank_play_sim_query.alias";

static const std::string kPoolGraphsageVip = "1038";
static const int kPinnerFormerHistNum = 100;
static const std::string kUnifiedQKI2IBiz = "i2i_base";

static const std::string kMinisterInterestCompleteVipServerName = "trpc.Serving.MinisterRecallByInterestDssmV1ServerNM.PredictInterfaceObj";
static const std::string kMinisterInterestCompleteVipEfIndex = "minister_recall_by_interest_dssm_vip_v1.alias";
static const std::string kMinisterInterestCompleteVipUserVector = "user_vec_norm";
static const int kMinisterInterestCompleteEmbDim = 96;

static const int kToplistHotSongID = 26;
static const int kToplistBoomSongID = 62;

static const int kgSingleRecallMid = 192002625;
static const int kgSingleRecallGzCid = 323036;  // k歌单路召回， 广州l5 cid
static const int kRecallU2u2iStrategyId = 58;
static const int kRecallU2u2iCalleeId = 205362896;
static const int kRecallU2u2iCalleeIfc = 180005332;
static const int kRecallTerasearchI2iCalleeIfc = 180012212;

// k歌聚合平台单曲集群l5
static const int kgUnionRecallMinisterMid = 192002625;
static const int kgUnionRecallMinisterCid = 704986;
static const int kRecallTerasearchU2iCalleeIfc = 180012216;

static const int kRecallFriendMid = 192002625;
static const int kRecallFriendCid = 323036;
static const int kRecallFriendStrategyId = 186;
static const int kRecallFriendCalleeId = 298004903;
static const int kRecallFriendCalleeIfc = 180003646;

static const int kEFEmbeedingSize = 40;
static const int kEFFavoItemSize = 10;
static const int kEFSelfItemSize = 10;
static const int kEFCutlItemSize = 10;
static const int kEFListItemSize = 10;
static const int kEFBatchSize = 20;
static const int kLowAssetUserSongNum = 10;
static const int kMidAssetUserSongNum = 50;
static const float kNoLowMaxScore = 0.008;
static const int kFollowTimeOut = 50;
static const int kRecallForLastTimeout = 80;
static const int KDSSMFetchEmbedTimeout = 50;
static const int KQKFetchEmbedTimeout = 70;
static const int KPinnerFormerFetchEmbedTimeout = 50;
static const int KDSSMSeqQueryTimeout = 70;
static const int kSimSingerTopSongSingerNum = 30;
static const int kSimSingerTopSongMaxSingerNum = 30;
static const int kSimSingerMid = 192002625;
static const int kSimSingerCid = 286242;
static const int kGNNKNN = 600;
static const int MinVipAssertNum = 100;

static const string KUnifiedPremInsertRecall = "unified-prem-insert";
static const string KSongSearchInsertRecall = "song-search-insert";
static const string KSongMatrerialInsertRecall = "song-matrerial-insert";

static const int PreChunkSize = 1024 * 10; // 10kb

// 无用的定义
define_recall_para(unified_wesing, 100, 600, kRecallTimeMs, "S99", -1);          // 大家都在唱
define_recall_para(wx_uin, 5, 100, kRecallTimeMs, "S99", -1);                    // 微信召回
define_recall_para(new_song, 10, 100, kRecallForLastTimeout, "S99", -1);         // 关注歌手的新歌
define_recall_para(new_song_hot, 10, 200, kRecallTimeMs, "S99", -1);             // 关注歌手的新歌 Embdding
define_recall_para(new_song_insert_prem, 1, 20, kRecallTimeMs, "S99", -1);       // 关注歌手的新歌 Embdding
define_recall_para(pf_similar_prem_singer, 20, 100, kRecallTimeMs, "S99", -1);   // 大家都在听相似歌手热门池
define_recall_para(qk_seq_prem, 100, 500, kRecallTimeMs, "S99", -1);             // 大家都在听的Q音酷狗联合深度召回
define_recall_para(qk_seq_i2i_song, 100, 500, kRecallTimeMs, "S99", -1);         // Q音酷狗联合深度召回I2I[歌曲模块]
define_recall_para(qk_seq_i2i_singer, 100, 500, kRecallTimeMs, "S99", -1);       // Q音酷狗联合深度召回I2I[歌手模块]
define_recall_para(qk_seq_i2i_prem, 100, 500, kRecallTimeMs, "S99", -1);         // Q音酷狗联合深度召回I2I[大家都在听模块]
define_recall_para(qk_seq_sasrec_prem, 100, 500, kRecallTimeMs, "S99", -1);      // 大家都在听的Q音酷狗联合深度召回 sasrec
define_recall_para(fav_dssm_song_prem, 100, 500, kRecallTimeMs, "S99", -1);      // 大家都在听的收藏DSSM深度召回
define_recall_para(pf_similar_song_singer, 10, 100, kRecallTimeMs, "S99", -1);   // 歌曲的歌手相似歌手热门池
define_recall_para(pf_similar_singer, 10, 100, kRecallTimeMs, "S99", -1);        // 相似歌手热门池
define_recall_para(profile_rejust_new, 60, 500, kRecallTimeMs, "S99", -1);       // 可调节画像召回
define_recall_para(profile_rejust_old, 60, 500, kRecallTimeMs, "S99", -1);       // 可调节画像召回
define_recall_para(profile_rejust_unique, 60, 500, kRecallTimeMs, "S99", -1);    // 可调节画像召回
define_recall_para(profile_rejust_main, 60, 500, kRecallTimeMs, "S99", -1);      // 可调节画像召回
define_recall_para(unified_song_musician, 10, 100, kRecallTimeMs, "S99", -1);    // 音乐人池子召回
define_recall_para(unified_singer_musician, 10, 200, kRecallTimeMs, "S99", -1);  // 音乐人池子召回
define_recall_para(unified_vip_free, 300, 1200, kRecallTimeMs, "S99", -1);       // 无免模式弹窗
define_recall_para(hot_new_content, 200, 500, kRecallTimeMs, "S99", -1);         // 内容中台新热池 100+内容小池子

// 召回通路
define_recall_para(profile_rejust_small, 60, 500, kRecallTimeMs, "S11_L2", 1);      // 可调节画像召回
define_recall_para(profile_rejust_hot, 60, 500, kRecallTimeMs, "S12_L2", 2);        // 可调节画像召回
define_recall_para(musiceye_genre, 100, 200, kRecallForLastTimeout, "S4_L0", 3);    // MusicEye根据流派召回 兜底召回
define_recall_para(musiceye_scenario, 80, 200, kRecallForLastTimeout, "S5_L0", 4);  // MusicEye根据场景流派召回 兜底召回
define_recall_para(unified_genre, 50, 300, kRecallTimeMs, "S9_L2", 5);              // 统一召回根据流派召回
define_recall_para(unified_scenario, 60, 200, kRecallTimeMs, "S10_L2", 6);          // 统一召回根据场景流派召回
define_recall_para(unified_singer, 200, 600, kRecallTimeMs, "S0_L2", 7);            // 统一召回根据歌手召回
define_recall_para(unified_singernew, 200, 600, kRecallTimeMs, "S0_L2", 23);            // 统一召回根据歌手召回
define_recall_para(unified_song, 200, 300, kRecallTimeMs, "S1_L2", 8);              // 统一召回根据歌曲召回
define_recall_para(unified_gnn_prem, 200, 300, kRecallTimeMs, "S2_L2", 9);          // 统一召回根据GNN召回
define_recall_para(unified_ip_song, 100, 200, kRecallTimeMs, "S1_L1", 10);          // 统一召回根据ip召回[歌曲模块](已消融)
define_recall_para(unified_ip_prem, 100, 200, kRecallTimeMs, "S2_L1", 11);          // 统一召回根据ip召回[大家都在听模块](已消融)
define_recall_para(unified_kg_song, 100, 200, kRecallTimeMs, "S1_L1", 12);          // 统一召回根据图谱作词作曲召回[歌曲模块]
define_recall_para(unified_kg_singer, 100, 200, kRecallTimeMs, "S0_L1", 13);        // 统一召回根据图谱作词作曲召回[歌手模块]
define_recall_para(unified_song_prem, 250, 800, kRecallTimeMs, "S2_L2", 14);        // 统一召回根据相似歌曲精选池召回
// define_recall_para(unified_song_prem_bak, 100, 400, kRecallTimeMs, "S2_L2", 15);    // 统一召回根据相似歌曲精选池召回
define_recall_para(pf_prem_singer, 40, 200, kRecallTimeMs, "S2_L1", 16);            // 大家都在听歌手热门池
define_recall_para(friend_song_prem, 30, 100, kRecallTimeMs, "S2_L1", 17);          // 大家都在听好友歌曲召回
define_recall_para(pf_singer, 5, 50, kRecallTimeMs, "S0_L1", 18);                   // 歌手热门池
define_recall_para(sim_singer_song, 100, 200, kRecallTimeMs, "S0_L2", 19);          // 歌手相似歌曲
define_recall_para(sim_singer_top_song, 100, 200, kRecallTimeMs, "S0_L2", 20);      // 歌手相似歌手热歌
define_recall_para(graphsage_song, 100, 200, kRecallTimeMs, "S1_L2", 21);           // 基于图的相似歌曲召回
define_recall_para(pf_song_singer, 5, 50, kRecallTimeMs, "S1_L1", 22);              // 歌曲的歌手热门池
// define_recall_para(tf_genre, 10, 200, kRecallForLastTimeout, "S9_L0", 23);          // 投放流派召回
define_recall_para(tf_singer, 10, 200, kRecallForLastTimeout, "S0_L0", 24);         // 投放歌手召回
define_recall_para(tf_unified, 10, 200, kRecallForLastTimeout, "S2_L0", 25);        // 投放大家都在听召回
define_recall_para(tf_song, 10, 200, kRecallForLastTimeout, "S1_L0", 26);           // 投放大家都在听召回
define_recall_para(times_genre, 100, 200, kRecallTimeMs, "S7_L2", 27);              // 年代+流派推荐
define_recall_para(times_language, 100, 200, kRecallTimeMs, "S8_L2", 28);           // 年代+语言推荐
define_recall_para(new_song_fa7, 10, 300, kRecallTimeMs, "S6_L0", 29);              // 关注歌手的新歌 FA7
define_recall_para(new_song_emb, 10, 200, kRecallTimeMs, "S6_L2", 30);              // 关注歌手的新歌 Embdding
define_recall_para(swing_song_prem, 80, 800, kRecallTimeMs, "S2_L1", 31);           // swing召回根据相似歌曲精选池召回(已消融)
define_recall_para(swing_singer, 30, 600, kRecallTimeMs, "S0_L1", 32);              // swing召回根据歌手召回
define_recall_para(swing_song, 30, 300, kRecallTimeMs, "S1_L1", 33);                // swing召回根据歌曲召回
define_recall_para(dssm_song_prem, 100, 500, kRecallTimeMs, "S2_L2", 34);           // 大家都在听的DSSM深度召回(已消融)
define_recall_para(dssm_song_cold_prem, 100, 500, kRecallTimeMs, "S2_L2", 35);      // 大家都在听的DSSM深度召回-长尾
define_recall_para(unified_qk_i2i_song, 100, 500, kRecallTimeMs, "S1_L2", 36);      // Q音酷狗联合深度召回I2I[歌曲模块] 统一召回
define_recall_para(unified_qk_i2i_singer, 100, 500, kRecallTimeMs, "S0_L2", 37);    // Q音酷狗联合深度召回I2I[歌手模块] 统一召回
define_recall_para(unified_qk_sasrec_prem, 100, 500, kRecallTimeMs, "S2_L2", 38);  // 大家都在听的Q音酷狗联合深度召回 sasrec 统一召回
define_recall_para(sdm_song_prem, 100, 500, kRecallTimeMs, "S2_L2", 39);           // 大家都在听的SDM深度召
define_recall_para(sasrec_song_prem, 100, 500, kRecallTimeMs, "S2_L2", 41);        // 大家都在听的Sasrec深度召回（已下线）
define_recall_para(pinner_former_prem, 100, 500, kRecallTimeMs, "S2_L2", 42);      // 大家都在听的pinnerformer召回
define_recall_para(unified_pinner_former_prem, 100, 500, kRecallTimeMs, "S2_L2", 43);       // 大家都在听的pinnerformer召回 全场景
define_recall_para(unified_comirec_prem, 100, 500, kRecallTimeMs, "S2_L2", 44);             // 大家都在听的comirec召回 全场景
define_recall_para(dssm_inner_song, 100, 500, kRecallTimeMs, "S1_L2", 45);                  // 歌曲模块的DSSM深度召回
define_recall_para(dssm_inner_singer, 100, 500, kRecallTimeMs, "S0_L2", 46);                // 歌手模块的DSSM深度召回
// define_recall_para(remote_dssm_song_prem, 100, 500, kRecallTimeMs, "S2_L2", 47);            // 大家都在听的日推DSSM深度召回
define_recall_para(remote_int_dssm_song_prem, 150, 500, kRecallTimeMs, "S2_L2", 48);        // 大家都在听的日推DSSM深度召回(互动目标
define_recall_para(remote_div_dssm_song_prem, 150, 1000, kRecallTimeMs, "S2_L2", 49);       // 大家都在听的日推DSSM深度召回(多兴趣
define_recall_para(full_u2u_song_prem, 50, 300, 120, "S2_L2", 50);                          // 大家都在听的u2u的完播
define_recall_para(fav_u2u_song_prem, 50, 300, 120, "S2_L2", 51);                           // 大家都在听的u2u的收藏
define_recall_para(profile_rejust, 30, 500, kRecallTimeMs, "S2_L2", 52);                    // 可调节画像召回
define_recall_para(unified_prem_musician, 40, 500, kRecallTimeMs, "S2_L2", 53);             // 音乐人池子召回(已消融)
define_recall_para(unified_vip_limited, 150, 1000, kRecallTimeMs, "S3_L2", 54);             // 非会员无免内容召回
define_recall_para(search_vip_limited, 5, 10, kRecallTimeMs, "S3_L1", 55);                  // 非会员无免内容召回 搜索
define_recall_para(singer_vip_limited, 10, 100, kRecallTimeMs, "S3_L0", 56);                // 非会员无免内容召回 收藏歌手
define_recall_para(graphsage_vip_song, 100, 200, kRecallTimeMs, "S3_L2", 57);               // 基于图的vip相似歌曲召回
define_recall_para(bottom_vip_limited, 100, 500, kRecallTimeMs, "S3_L0", 58);               // 非会员无免内容召回 兜底热歌
// define_recall_para(dssm_vip_limited, 100, 500, kRecallTimeMs, "S3_L2", 59);                 // 无免内容的全局DSSM深度召回
define_recall_para(dssm_vip_limitednew, 100, 500, kRecallTimeMs, "S3_L2", 63);                 // 无免内容的全局DSSM深度召回
define_recall_para(dssm_inner_complete_vip_limited, 100, 500, kRecallTimeMs, "S3_L2", 60);  // 无免内容的场景内DSSM深度召回
define_recall_para(new_user_fa7, 200, 500, kRecallTimeMs, "S2_L0", 61);                     // 新用户池子 2000内容池子
define_recall_para(dssm_inner_interest_complete, 100, 500, kRecallTimeMs, "S2_L2", 62);    // 大家都在听的场景内DSSM深度召回-完播目标
define_recall_para(dssm_inner_interest_i2i_song, 100, 500, kRecallTimeMs, "S1_L2", 65);    // 场景内DSSM深度召回-i2i歌曲
define_recall_para(dssm_inner_interest_i2i_singer, 100, 500, kRecallTimeMs, "S0_L2", 66);  // 场景内DSSM深度召回-i2i歌手
define_recall_para(dssm_cross_interest_i2i_song, 100, 500, kRecallTimeMs, "S1_L2", 67);    // 跨场景DSSM深度召回-i2i歌曲
define_recall_para(dssm_cross_interest_i2i_singer, 100, 500, kRecallTimeMs, "S0_L2", 68);  // 跨场景DSSM深度召回-i2i歌手
define_recall_para(kg_glove_i2i_song, 100, 500, kRecallTimeMs, "S1_L2", 69);               // 酷狗glove-i2i歌曲
define_recall_para(kg_glove_i2i_singer, 100, 500, kRecallTimeMs, "S0_L2", 70);             // 酷狗glove-i2i歌手
define_recall_para(kg_glove_i2i_prem, 100, 500, kRecallTimeMs, "S2_L2", 71);               // 酷狗glove-i2i大家都在听模块
define_recall_para(gnn_cross_interest_i2i_prem, 100, 500, kRecallTimeMs, "S2_L2", 72);     // 场景内GNN深度召回-大家都在听
define_recall_para(gnn_cross_interest_i2i_song, 100, 500, kRecallTimeMs, "S1_L2", 73);     // 场景内GNN深度召回-i2i歌曲
define_recall_para(gnn_cross_interest_i2i_singer, 100, 500, kRecallTimeMs, "S0_L2", 74);   // 场景内GNN深度召回-i2i歌手
define_recall_para(dssm_cross_interest_i2i_prem, 100, 500, kRecallTimeMs, "S2_L2", 75);    // 全场景DSSMi2i深度召回-大家都在听
define_recall_para(unify_u2u2i_prem, 100, 500, kRecallTimeMs, "S2_L1", 76);                // 全场景u2u2i深度召回-大家都在听
define_recall_para(dssm_inner_song_cold, 100, 500, kRecallTimeMs, "S1_L2", 113);           // 歌曲模块的DSSM深度召回--长尾
define_recall_para(dssm_inner_singer_cold, 100, 500, kRecallTimeMs, "S0_L2", 113);         // 歌手模块的DSSM深度召回--长尾

define_recall_para(tdm_song_prem, 100, 500, kRecallTimeMs, "S2_L2", 77);      // TDM 大家都在听
define_recall_para(tdm_song_module, 100, 500, kRecallTimeMs, "S1_L2", 77);    // TDM 歌曲模块
define_recall_para(tdm_singer_module, 100, 500, kRecallTimeMs, "S0_L2", 77);  // TDM 歌手模块

define_recall_para(reco_candidate_recall, 200, 500, kRecallTimeMs, "S2_L1", 78);         // 大家都在听重召回
define_recall_para(reco_candidate_song_recall, 200, 500, kRecallTimeMs, "S1_L1", 78);    // 歌曲重召回
define_recall_para(reco_candidate_singer_recall, 200, 500, kRecallTimeMs, "S0_L1", 78);  // 歌手重召回
define_recall_para(reco_candidate_vip_recall, 200, 500, kRecallTimeMs, "S3_L1", 78);     // VIP重召回

define_recall_para(content_recall_prem, 100, 500, kRecallTimeMs, "S2_L2", 79);    // 大家都在听-垂类召回

define_recall_para(dssm_cross_interest_favor, 100, 500, kRecallTimeMs, "S2_L2", 82);     // 大家都在听的全场景DSSM深度召回-收藏目标
define_recall_para(comirec_cross_prem, 100, 500, kRecallTimeMs, "S2_L2", 83);            // 大家都在听的全场景comirec深度召回
define_recall_para(kg_unify_cross_sasrec_prem, 100, 500, kRecallTimeMs, "S2_L2", 84);    // 大家都在听的全场景sasrec深度召回
define_recall_para(cross_sasrec_i2i_song, 100, 500, kRecallTimeMs, "S1_L2", 85);         // 全场景sasrec-i2i歌曲
define_recall_para(cross_sasrec_i2i_singer, 100, 500, kRecallTimeMs, "S0_L2", 86);       // 全场景sasrec-i2i歌手
define_recall_para(kg_unify_cross_star_prem, 100, 500, kRecallTimeMs, "S2_L2", 87);      // 大家都在听的跨场景star度召回--统一平台
define_recall_para(cross_star_i2i_song, 100, 500, kRecallTimeMs, "S1_L2", 88);           // 跨场景star-i2i歌曲
define_recall_para(cross_star_i2i_singer, 100, 500, kRecallTimeMs, "S0_L2", 89);         // 跨场景star-i2i歌手
define_recall_para(dapan_recall_prem, 100, 500, kRecallTimeMs, "S2_L2", 90);             // 大盘召回

// pdn
define_recall_para(pdn_song_prem, 100, 500, kRecallTimeMs, "S2_L2", 91);  // 大家都在听的pdn召回
define_recall_para(pdn_song, 100, 500, kRecallTimeMs, "S1_L2", 91);       // 歌曲模块的pdn召回
define_recall_para(pdn_singer, 100, 500, kRecallTimeMs, "S0_L2", 91);     // 歌手模块的pdn召回

// MPE I2I
define_recall_para(mpe_i2i_song, 100, 500, kRecallTimeMs, "S1_L2", 92);       // 歌曲模块的MPE I2I召回
define_recall_para(mpe_i2i_singer, 100, 500, kRecallTimeMs, "S0_L2", 92);     // 歌手模块的MPE I2I召回
// LLM 召回
define_recall_para(llm_recall_song_prem, 100, 500, kRecallTimeMs, "S2_L2", 93);   // LLM召回 大家都在听
define_recall_para(llm_recall_song, 100, 500, kRecallTimeMs, "S1_L2", 93);        // LLM召回 歌曲
define_recall_para(llm2_recall_song, 100, 500, kRecallTimeMs, "S1_L2", 94);       // LLM-MTP召回 歌曲

define_recall_para(inner_expand_prem, 100, 500, kRecallTimeMs, "S2_L2", 95);               // 大家都在听的扩列召回
define_recall_para(inner_expand_song, 100, 500, kRecallTimeMs, "S1_L2", 95);               // 歌曲模块的扩列召回
define_recall_para(inner_expand_singer, 100, 500, kRecallTimeMs, "S0_L2", 95);             // 歌手模块的扩列召回
define_recall_para(sim_inner_expand, 100, 500, kRecallTimeMs, "S27_L1", 95);               // 相似模块扩列召回

// 蒸馏
// define_recall_para(distill_comp_song_prem, 25, 500, kRecallTimeMs, "S2_L2", 94);       // 大家都在听蒸馏完播召回
// define_recall_para(distill_comp_song, 100, 500, kRecallTimeMs, "S1_L2", 95);           // 歌曲模块的蒸馏完播召回
// define_recall_para(distill_comp_singer, 100, 500, kRecallTimeMs, "S0_L2", 96);         // 歌手模块的蒸馏完播召回
// define_recall_para(distill_long_play_song_prem, 25, 500, kRecallTimeMs, "S2_L2", 97);  // 大家都在听蒸馏长播召回
// define_recall_para(distill_long_play_song, 100, 500, kRecallTimeMs, "S1_L2", 98);      // 歌曲模块的蒸馏长播召回
// define_recall_para(distill_long_play_singer, 100, 500, kRecallTimeMs, "S0_L2", 99);    // 歌手模块的蒸馏长播召回

// 资产召回
// 内部资产插入召回
define_recall_para(asset_profile, 100, 10, kRecallTimeMs, "S2_L1", 100);  // 资产召回-profile
define_recall_para(asset_favo, 100, 10, kRecallTimeMs, "S2_L1", 101);     // 资产召回-favo
define_recall_para(reco_reason, 10, 10, kRecallTimeMs, "S1_L1", 102);     // 推荐理由

// 新歌召回
define_recall_para(inner_newsong_prem, 100, 500, kRecallTimeMs, "S2_L2", 103);               // 大家都在听的pdn召回
define_recall_para(inner_newsong_song, 100, 500, kRecallTimeMs, "S1_L2", 103);               // 歌曲模块的pdn召回
define_recall_para(inner_newsong_singer, 100, 500, kRecallTimeMs, "S0_L2", 103);             // 歌手模块的pdn召回
define_recall_para(prem_simsinger_newsong_recall, 200, 200, kRecallTimeMs, "S2_L1", 104);    // 大家都在听u2simsinger新歌
define_recall_para(song_simsinger_newsong_recall, 200, 200, kRecallTimeMs, "S1_L1", 104);    // 歌曲模块u2simsinger新歌
define_recall_para(singer_simsinger_newsong_recall, 200, 200, kRecallTimeMs, "S0_L1", 104);  // 歌手模块u2simsinger新歌
define_recall_para(diffurec_prem, 100, 500, kRecallTimeMs, "S2_L2", 105);                    // 大家都在听的扩散召回
define_recall_para(diffurec_song, 100, 500, kRecallTimeMs, "S1_L2", 105);                    // 歌曲模块的扩散召回
define_recall_para(diffurec_sim, 200, 500, kRecallTimeMs, "S27_L1", 105);                         // 大盘召回

// 新用户模块召回
define_recall_para(new_user, 500, 500, kRecallTimeMs, "S24_L0", 61);      // 新用户模块池子 2000内容池子
define_recall_para(new_user_vip, 500, 500, kRecallTimeMs, "S25_L0", 61);  // 新用户模块池子 2000内容池子

// 外部资产非插入召回
define_recall_para(kk_asset_prem, 100, 100, kRecallTimeMs, "S2_L1", 106);  // 大家都在听-双酷资产召回

// vip插入资产
define_recall_para(asset_vip, 1000, 1000, kRecallTimeMs, "S3_L1", 107);  // vip模块-资产召回

// 回流用户专区
define_recall_para(asset_return_user, 30, 30, kRecallTimeMs, "S26_L1", 108);           // 回流用户-资产召回
define_recall_para(profile_return_user, 30, 30, kRecallTimeMs, "S26_L1", 109);         // 回流用户-画像召回
define_recall_para(annualreview_return_user, 200, 200, kRecallTimeMs, "S26_L1", 110);  // 回流用户-年度总结歌曲召回
define_recall_para(friendsongs_return_user, 200, 200, kRecallTimeMs, "S26_L1", 111);   // 回流用户-好友召回
define_recall_para(toplist_return_user, 200, 200, kRecallTimeMs, "S26_L1", 112);       // 回流用户-热榜召回

// 新歌通路
define_recall_para(prem_newsong_recall, 200, 200, kRecallTimeMs, "S2_L0", 114);    // 大家都在听新歌
define_recall_para(song_newsong_recall, 200, 200, kRecallTimeMs, "S1_L0", 114);    // 歌曲模块新歌
define_recall_para(singer_newsong_recall, 200, 200, kRecallTimeMs, "S0_L0", 114);  // 歌手模块新歌

// 长序列i2i
define_recall_para(sim_i2i_song_prem, 100, 500, kRecallTimeMs, "S2_L2", 115);  // 大家都在听的pdn召回
define_recall_para(sim_i2i_song, 100, 500, kRecallTimeMs, "S1_L2", 115);       // 歌曲模块的pdn召回
define_recall_para(sim_i2i_singer, 100, 500, kRecallTimeMs, "S0_L2", 115);     // 歌手模块的pdn召回

// TODO(karinesun) 改造一下 这个定义太死了 同一条召回可以属于多个模块
define_recall_para(sim_reco_candidate, 200, 500, kRecallTimeMs, "S27_L1", 116);                // 重召回
define_recall_para(sim_dssm_inner_interest_complete, 100, 500, kRecallTimeMs, "S27_L1", 117);  // dssm场景内-完播
define_recall_para(sim_pf_singer, 20, 100, kRecallTimeMs, "S27_L1", 118);                      // 歌手热歌
define_recall_para(sim_unified_song, 200, 500, kRecallTimeMs, "S27_L1", 119);                  // i2i
define_recall_para(sim_dssm_cross_interest_i2i, 200, 500, kRecallTimeMs, "S27_L1", 120);       // 跨场景i2i
define_recall_para(sim_dapan, 200, 500, kRecallTimeMs, "S27_L1", 121);                         // 大盘召回
define_recall_para(sim_trigger_songs, 50, 150, kRecallTimeMs, "S27_L1", 122);                  // trigger插入召回
// 最近常听
define_recall_para(sim_reco_candidate_recent, 200, 500, kRecallTimeMs, "S28_L1", 116);                // 重召回
define_recall_para(sim_dssm_inner_interest_complete_recent, 100, 500, kRecallTimeMs, "S28_L1", 117);  // dssm场景内-完播
define_recall_para(sim_pf_singer_recent, 20, 100, kRecallTimeMs, "S28_L1", 118);                      // 歌手热歌
define_recall_para(sim_unified_song_recent, 200, 500, kRecallTimeMs, "S28_L1", 119);                  // i2i
define_recall_para(sim_dssm_cross_interest_i2i_recent, 200, 500, kRecallTimeMs, "S28_L1", 120);       // 跨场景i2i
define_recall_para(sim_dapan_recent, 200, 500, kRecallTimeMs, "S28_L1", 121);                         // 大盘召回
define_recall_para(sim_trigger_songs_recent, 50, 150, kRecallTimeMs, "S28_L1", 122);                  // trigger插入召回

// 垂类shelf重构，使用新场景和recall root name
define_recall_para(vertical_dssm_inner_interest_complete, 100, 500, kRecallTimeMs, "S29_L2", 117);   // dssm场景内-完播
define_recall_para(vertical_unified_song, 100, 500, kRecallTimeMs, "S29_L2", 119);                   // i2i--root recall
define_recall_para(vertical_dssm_cross_interest_i2i, 100, 500, kRecallTimeMs, "S29_L2", 120);        // 跨场景i2i
define_recall_para(vertical_dapan, 100, 500, kRecallTimeMs, "S29_L2", 121);                          // 大盘召回

// 投放为收藏计费的歌曲单独加的召回-歌曲数量少 from 音乐推需求
define_recall_para(favor_target_delivery_singer, 30, 100, kRecallTimeMs, "S0_L2", 123);       // 歌手模块-投放收藏计费模式任务召回
define_recall_para(favor_target_delivery_song, 30, 100, kRecallTimeMs, "S1_L2", 123);         // 歌曲模块-投放收藏计费模式任务召回
define_recall_para(favor_target_delivery_prem, 30, 100, kRecallTimeMs, "S2_L2", 123);         // 大家都在听-投放收藏计费模式任务召回
define_recall_para(favor_target_delivery_vip_limited, 30, 100, kRecallTimeMs, "S3_L2", 123);  // VIP限免模块-投放收藏计费模式任务召回
define_recall_para(favor_target_delivery_sim_trigger, 30, 100, kRecallTimeMs, "S27_L2", 123); // 资产相似模块-投放收藏计费模式任务召回
define_recall_para(favor_target_delivery_sim_trigger_recent, 30, 100, kRecallTimeMs, "S28_L2", 123); // 最近常听模块-投放收藏计费模式任务召回

define_recall_para(tf_vip_limited, 10, 200, kRecallForLastTimeout, "S3_L0", 124);             // 投放Vip限免召回
define_recall_para(tf_sim_trigger, 10, 200, kRecallForLastTimeout, "S27_L0", 125);            // 投放资产相似召回
define_recall_para(tf_sim_trigger_recent, 10, 200, kRecallForLastTimeout, "S28_L0", 126);     // 投放最近常听模块召回

// 投放不需要透传的特征
static const std::unordered_set<std::string> notPass2DeliveryFeatures = {
    "u_daily_click_singers",
    "u_daily_click_songs",
    "u_daily_complete_singers",
    "u_daily_complete_songs",
    "u_minister_click_singers",
    "u_minister_click_songs",
    "u_minister_complete_singers",
    "u_minister_complete_songs",
    "u_minister_cut_singers",
    "u_minister_cut_songs",
    "u_minister_complete_asset_singers",
    "u_minister_complete_asset_songs",
    "u_phonebrand",
    "u_phonemodel",
    "u_phoneprice",
    "u_ninecrowd",
};

// Zigzag Scene 定义
static const std::unordered_map<std::string, std::string> kZigzagSceneMap = {{"S0", k_recall_unified_singer_name},
                                                                             {"S1", k_recall_unified_song_name},
                                                                             {"S2", k_recall_unified_song_prem_name},
                                                                             {"S3", k_recall_unified_vip_limited_name},
                                                                             {"S4", k_recall_musiceye_genre_name},
                                                                             {"S5", k_recall_musiceye_scenario_name},
                                                                             {"S6", k_recall_new_song_name},
                                                                             {"S7", k_recall_times_genre_name},
                                                                             {"S8", k_recall_times_language_name},
                                                                             {"S9", k_recall_unified_genre_name},
                                                                             {"S10", k_recall_unified_scenario_name},
                                                                             {"S11", k_recall_profile_rejust_small_name},
                                                                             {"S12", k_recall_profile_rejust_hot_name},
                                                                             {"S24", k_recall_new_user_name},
                                                                             {"S25", k_recall_new_user_vip_name},
                                                                             {"S26", k_recall_profile_return_user_name},
                                                                             {"S27", k_recall_sim_trigger_songs_name},
                                                                             {"S28", k_recall_sim_trigger_songs_recent_name},
                                                                             {"S29", k_recall_vertical_unified_song_name}};

// Zigzag Layer 定义
static const std::vector<std::string> kZigZagLayerVector = {"L0", "L1", "L2"};

// Zigzag 子任务截断数量定义, Scene Layer的定义如{define_recall_para}中定义
static const std::unordered_map<std::string, int> kZigzagQuotaMap = {{"S0_L0", 200},  {"S0_L1", 500},  {"S0_L2", 2400}, {"S1_L0", 200},
                                                                     {"S1_L1", 500},  {"S1_L2", 2400}, {"S2_L0", 200},  {"S2_L1", 500},
                                                                     {"S2_L2", 2400}, {"S3_L0", 400},  {"S3_L1", 100},  {"S3_L2", 1000},
                                                                     {"S29_L0", 400},  {"S29_L1", 100},  {"S29_L2", 3000}};

// 粗排每个召回模块进精排的个数，没有标注的话默认50个
static const std::unordered_map<std::string, int> kRoughSortNumMap = {
    {k_recall_hot_new_content_name, 100},   {k_recall_musiceye_genre_name, 50},   {k_recall_musiceye_scenario_name, 50},
    {k_recall_unified_song_prem_name, 200}, {k_recall_unified_singer_name, 200},  {k_recall_unified_genre_name, 60},
    {k_recall_unified_song_name, 200},      {k_recall_unified_scenario_name, 50}, {k_recall_new_song_name, 20},
    {k_recall_times_genre_name, 50},        {k_recall_times_language_name, 50},   {k_recall_unified_vip_limited_name, 100},
    {k_recall_sim_trigger_songs_name, 150}, {k_recall_sim_trigger_songs_recent_name, 150}, {k_recall_vertical_unified_song_name, 200}};
static const std::unordered_map<int, float> kRecallTypeDefaultMap = {{1, 0.501}, {2, 0.501}, {3, 0.500}, {4, 0.499},
                                                                     {5, 0.500}, {7, 0.499}, {8, 0.499}, {9, 0.498}};
// 某些召回不需要同歌组替换
// static const std::unordered_set<std::string> kRecallNotNeedReplceBySameGroup = {
//     k_recall_new_song_fa7_name,   k_recall_new_song_emb_name,   k_recall_new_song_hot_name,
//     k_recall_new_song_name,       k_recall_pf_prem_singer_name, k_recall_pf_similar_prem_singer_name,
//     k_recall_pf_singer_name,      k_recall_pf_song_singer_name, k_recall_tf_song_name, 
//     k_recall_tf_vip_limited_name, k_recall_tf_sim_trigger_name, k_recall_tf_sim_trigger_recent_name,
//     k_recall_tf_singer_name,      k_recall_tf_unified_name,     k_recall_unified_song_prem_bak_name,
//     k_recall_unified_wesing_name,
// };
// static const std::unordered_set<std::string> kRecallNeedReplaceBySameGroup = std::unordered_set<std::string>{};

// 外显标签参数控制
// static const int kLabelTimeOut = 40;
// static const std::vector<std::string> kLabelForNewSong = {"今日新歌", "本周新歌", "新歌推荐"};
// static const std::string kLabelForVipSinger = "你最近收藏过的歌手";
// static const std::string kLabelForVipSearch = "你最近搜索过的歌手";
// static const std::unordered_map<uint32_t, std::string> kLabelGenreNameMap = {
//     {352, "摇滚"},       {1117, "电子音乐"},      {119, "说唱"},          {1964, "EDM"},         {254, "电音流行"},   {382, "流行摇滚"},
//     {2251, "抒情流行"},  {29, "古典音乐"},        {282, "R&B"},           {126, "流行说唱"},     {256, "舞曲流行"},   {2007, "House"},
//     {98, "爵士乐"},      {283, "当代节奏布鲁斯"}, {150, "民谣"},          {320, "Indie Pop"},    {460, "Indie Rock"}, {139, "Trap Rap"},
//     {151, "当代民谣"},   {439, "另类摇滚"},       {319, "民谣流行"},      {353, "朋克"},         {317, "合成器流行"}, {293, "Soul"},
//     {159, "独立民谣"},   {1916, "Downtempo"},     {438, "硬摇滚"},        {1969, "Future Bass"}, {1967, "Dubstep"},   {287, "另类节奏布鲁斯"},
//     {257, "青少年流行"}, {451, "民谣摇滚"},       {321, "爵士流行"},      {251, "拉丁流行"},     {403, "前卫摇滚"},   {324, "Dream Pop"},
//     {1903, "Ambient"},   {461, "后摇"},           {364, "流行朋克"},      {121, "东海岸说唱"},   {258, "City Pop"},   {122, "西海岸说唱"},
//     {131, "爵士说唱"},   {311, "Funk"},           {103, "拉丁爵士"},      {318, "艺术流行"},     {2071, "Drum&Bass"}, {129, "硬核说唱"},
//     {387, "Britpop"},    {342, "乡村流行"},       {2006, "EDM Trap"},     {310, "Neo-Soul"},     {234, "Bossa Nova"}, {441, "迷幻摇滚"},
//     {443, "盯鞋"},       {452, "蓝调摇滚"},       {416, "数学摇滚"},      {133, "另类说唱"},     {158, "城市民谣"},   {123, "南部说唱"},
//     {4176, "人声爵士"},  {130, "匪帮说唱"},       {394, "新浪潮"},        {295, "流行灵魂乐"},   {1956, "蒸汽波"},    {434, "后朋克"},
//     {2249, "乡村摇滚"},  {2287, "情绪说唱"},      {2279, "当代都市流行"}, {237, "Smooth Jazz"},  {1988, "Trance"},    {226, "融合爵士"},
//     {456, "车库摇滚"},   {5740, "Chill Hop"},     {1977, "Techno"},
// };
// static const std::unordered_map<uint32_t, uint32_t> kLabelGenreCategoryMap = {
//     {352, 352},   {1117, 1117}, {119, 119}, {1964, 1117}, {254, 15},    {382, 352},   {2251, 15},   {29, 29},     {282, 282},   {126, 119},
//     {256, 15},    {2007, 1117}, {98, 98},   {283, 282},   {150, 150},   {320, 15},    {460, 352},   {139, 119},   {151, 150},   {439, 352},
//     {319, 15},    {353, 352},   {317, 317}, {293, 282},   {159, 150},   {1916, 1117}, {438, 352},   {1969, 1117}, {1967, 1117}, {287, 282},
//     {257, 15},    {451, 352},   {321, 15},  {251, 15},    {403, 352},   {324, 15},    {1903, 1117}, {461, 352},   {364, 352},   {121, 119},
//     {258, 15},    {122, 119},   {131, 119}, {311, 282},   {103, 98},    {318, 15},    {2071, 1117}, {129, 119},   {387, 352},   {342, 15},
//     {2006, 1117}, {310, 282},   {234, 98},  {441, 352},   {443, 352},   {452, 352},   {416, 352},   {133, 119},   {158, 150},   {123, 119},
//     {4176, 98},   {130, 119},   {394, 352}, {295, 282},   {1956, 1117}, {434, 352},   {2249, 352},  {2287, 119},  {2279, 15},   {237, 98},
//     {1988, 1117}, {226, 98},    {456, 352}, {5740, 1117}, {1977, 1117},
// };

// 过滤参数控制
static const int kBidPF = 270;
static const int kBatchPF = 15;
static const int kFilterSameGroupTimeout = 50;
static const int kFilterMinSongDuration = 120;
static const int kFilterMaxSongDuration = 3600;
// https://iwiki.woa.com/pages/viewpage.action?pageId=310751491
static const std::unordered_set<uint32_t> kFilterSongBadVersions = {3, 7, 8, 28, 29};
// static const int kFilterUserLowestGrade = 4;
// static const std::unordered_set<uint32_t> kFilterBadGrades = {5};
// static const std::unordered_set<uint32_t> kFilterWhiteSingers = {
//     2,       13,      20,      38,      42,      44,      47,      51,      53,      54,      62,      74,      87,      89,      96,      101,
//     107,     109,     112,     118,     120,     125,     131,     137,     138,     139,     141,     143,     146,     149,     159,     163,
//     165,     167,     168,     169,     171,     173,     174,     175,     182,     186,     202,     208,     210,     212,     218,     219,
//     224,     227,     228,     231,     235,     239,     245,     246,     259,     263,     264,     265,     269,     596,     800,     1066,
//     3347,    3376,    3954,    4190,    4199,    4246,    4284,    4286,    4349,    4351,    4355,    4357,    4359,    4362,    4365,    4367,
//     4419,    4420,    4422,    4442,    4445,    4449,    4470,    4533,    4558,    4569,    4601,    4604,    4605,    4607,    4610,    4615,
//     4619,    4620,    4625,    4651,    4658,    4674,    4685,    4701,    4713,    4715,    4857,    5035,    5040,    5062,    5081,    5119,
//     5556,    5924,    5943,    5979,    6028,    6078,    6090,    6351,    6385,    6397,    6499,    6592,    6740,    6950,    7033,    7131,
//     7221,    8105,    8524,    11446,   11449,   11453,   11464,   11476,   11606,   11608,   11626,   11707,   11979,   12111,   12132,   12196,
//     12303,   12322,   12530,   12721,   13203,   13461,   13537,   13578,   13930,   13948,   14013,   15514,   15580,   15679,   16244,   19010,
//     19329,   19535,   19624,   19631,   19633,   19854,   22529,   22704,   22874,   23800,   24833,   25724,   26287,   29858,   31035,   33680,
//     34412,   36239,   36240,   36691,   37459,   39657,   40449,   60433,   60505,   60894,   61386,   61955,   63099,   65313,   65887,   67008,
//     68632,   69205,   89698,   91580,   91610,   92117,   116322,  154330,  156640,  157983,  158556,  161301,  161432,  161444,  162021,  163293,
//     163550,  163751,  164158,  164219,  165242,  166260,  166331,  170683,  171061,  178612,  179603,  180646,  188132,  196465,  197898,  198135,
//     199509,  199515,  200521,  202409,  203269,  204664,  652850,  940634,  940748,  942991,  944274,  944658,  945054,  945829,  949559,  951320,
//     963482,  971203,  978065,  1005766, 1005787, 1010159, 1011983, 1012038, 1012042, 1016429, 1016794, 1029666, 1038252, 1060978, 1060982, 1060983,
//     1060985, 1093019, 1099829, 1102710, 1103924, 1113420, 1116812, 1121441, 1142841, 1167287, 1185026, 1199153, 1233588, 1235352, 1241893, 1278872,
//     1288409, 1327288, 1327483, 1358209, 1363757, 1378410, 1399808, 1456756, 1471877, 1473880, 1507533, 1507534, 1509135, 1521450, 1527896, 1530392,
//     1560445, 1573958, 1726831, 2079450, 2096528, 2096857, 2101458, 2121419, 2121439, 2121445, 2122093, 2122094, 2141217, 2141373, 2141439, 2141447,
//     2141484, 2141486, 2169579, 2169599, 2172838, 2176270, 2176319, 2178777, 2241311, 2249618, 2383826, 2696301, 2710229, 2743368, 2849003, 2905963,
//     3026094, 3069943, 3153093, 3168475, 3298773, 3344331, 3344766, 5292588, 5292606, 5292784};

// I2I流派池编码
static const uint32_t kI2ITSModId = 1881473;
static const uint32_t kI2ITSCmdId = 4456448;
static const uint32_t kI2IEFModId = 192002625;
static const uint32_t kI2IEFCmdId = 286246;
static const uint32_t kU2IEFModId = 192002625;
static const uint32_t kU2IEFCmdId = 286242;
static const uint32_t kI2IMusicianModId = 192002241;
static const uint32_t kI2IMusicianCmdId = 73915;
static const std::string kTSAllI2IPoolId = "22";
static const std::string kTSWhiteI2IPoolId = "23";
static const std::string kTSPremiumI2IPoolId = "49";
static const std::string kTSPremiumV2I2IPoolId = "115";
static const std::string kTSMusicianI2IPoolId = "113";
static const std::string kTSNewSongI2IPoolId = "84";
static const std::string kTSAdvanceWhiteI2IPoolId = "62";
static const std::string kTSWeSingI2IPoolId = "95";
static const std::string kTSVipI2IPoolId = "135";
static const std::unordered_map<std::string, std::string> kGenreI2IPoolMap = {
    {"4_15", "24"},   {"4_119", "25"}, {"5_15", "26"},  {"5_119", "27"}, {"5_352", "28"},  {"5_98", "29"},  {"5_282", "30"}, {"0_15", "31"},
    {"0_1134", "32"}, {"0_119", "33"}, {"0_352", "34"}, {"1_15", "35"},  {"0_282", "36"},  {"0_150", "37"}, {"1964", "38"},  {"9_576", "39"},
    {"29", "40"},     {"2194", "41"},  {"2198", "42"},  {"3_15", "50"},  {"3_1134", "51"}, {"3_119", "61"},
};
// 单曲自成一派的神奇逻辑
// from ai_go_proj/server/CAiRecTabSortSvr/Comm/Helper.go
static const std::unordered_map<std::string, std::string> kProfileRejustLanI2IPoolMap = {
    {"0_15", "721"}, // 国语
    {"5_15", "723"}, // 英语
    {"4_15", "725"}, // 韩语
    {"3_15", "724"}, // 日语
    {"9_576", "722"}, // 纯音乐
    {"1_15", "1351"}, // 粤语
};

// 其他参数
static const std::unordered_set<std::string> kGenreWhiteList = {"5_119", "0_119", "0_15",  "0_150", "3_15",   "3_1134", "0_352", "1_15",
                                                                "5_15",  "5_352", "5_98",  "0_282", "5_282",  "4_15",   "4_119", "29",
                                                                "1964",  "2194",  "9_576", "2198",  "0_1134", "3_119"};
static const std::unordered_set<std::string> kGenreAdvanceList = {"5_119", "0_119", "0_150", "3_15",  "3_1134", "0_352", "1_15",
                                                                  "5_15",  "5_352", "5_98",  "0_282", "5_282",  "4_15",  "4_119",
                                                                  "29",    "1964",  "2194",  "9_576", "0_1134", "3_119"};
static const std::unordered_map<std::string, std::string> kGenreIdNameMap = {
    {"5_119", "欧美说唱"}, {"0_119", "国语说唱"}, {"0_15", "华语好歌"},   {"0_150", "民谣"},      {"0_1134", "国语ACG"},
    {"3_15", "日语歌曲"},  {"3_1134", "日语ACG"}, {"0_352", "国产摇滚"},  {"1_15", "粤语歌"},     {"5_15", "英语流行"},
    {"5_352", "欧美摇滚"}, {"5_98", "爵士乐"},    {"5_282", "欧美R&B"},   {"4_15", "韩流歌曲"},   {"4_119", "韩语说唱"},
    {"29", "古典音乐"},    {"1964", "电子舞曲"},  {"2194", "国风音乐"},   {"1143", "国风音乐"},   {"9_576", "轻音乐"},
    {"0_282", "国语R&B"},  {"1142", "DJ舞曲"},    {"9_1117", "电子音乐"}, {"2198", "社交媒体风"}, {"3_119", "日语说唱"}};

// Note(wegozhang) 该字典用于下发负反馈使用 社交媒体风不展示
static const std::unordered_map<std::string, std::string> kGenreIdFeedbackNameMap = {
    {"5_119", "欧美说唱"}, {"0_119", "国语说唱"}, {"0_15", "国语流行"},  {"0_150", "国语民谣"}, {"0_1134", "国语ACG"},  {"3_15", "日语歌曲"},
    {"3_1134", "日语ACG"}, {"0_352", "国语摇滚"}, {"1_15", "粤语歌"},    {"5_15", "英语流行"},  {"5_352", "欧美摇滚"},  {"5_98", "爵士乐"},
    {"5_282", "欧美R&B"},  {"4_15", "韩语流行"},  {"4_119", "韩语说唱"}, {"29", "古典音乐"},    {"1964", "电子舞曲"},   {"2194", "国风音乐"},
    {"1143", "国风音乐"},  {"9_576", "轻音乐"},   {"0_282", "国语R&B"},  {"1142", "DJ舞曲"},    {"9_1117", "电子音乐"}, {"3_119", "日语说唱"}};

static const std::unordered_map<std::string, std::string> kGenreIdWhiteMap = {
    {"1714", "拉丁传统音乐"}, {"356", "先锋音乐"},    {"352", "摇滚"},      {"15", "流行"},       {"29", "古典音乐"},     {"119", "说唱"},
    {"175", "乡村"},          {"150", "民谣"},        {"1545", "世界音乐"}, {"487", "金属"},      {"576", "轻音乐"},      {"2133", "宗教音乐"},
    {"612", "雷鬼"},          {"1138", "儿童音乐"},   {"380", "新世纪"},    {"98", "爵士乐"},     {"1117", "电子音乐"},   {"353", "朋克"},
    {"282", "节奏布鲁斯"},    {"241", "布鲁斯"},      {"1964", "电子舞曲"}, {"2194", "中国风"},   {"2198", "社交媒体风"}, {"1143", "中国风"},
    {"1134", "ACG"},          {"1141", "喊麦"},       {"953", "电影"},      {"1089", "电视剧"},   {"1112", "音乐综艺"},   {"1111", "音乐比赛"},
    {"2234", "真人秀"},       {"2191", "数来宝说唱"}, {"1142", "DJ舞曲"},   {"3777", "恶搞版"},   {"2199", "大众流行风"}, {"3786", "革命歌曲"},
    {"2104", "红歌"},         {"2190", "军旅歌曲"},   {"2187", "草原风"},   {"2188", "高原风"},   {"4199", "西域风"},     {"4200", "川渝风"},
    {"2194", "中国风"},       {"2193", "国风"},       {"2195", "戏曲风"},   {"2196", "武侠风"},   {"2197", "民族风"},     {"2970", "民俗风"},
    {"3431", "当代民歌"},     {"1134", "ACG"},        {"352", "摇滚"},      {"15", "流行"},       {"119", "说唱"},        {"150", "民谣"},
    {"576", "轻音乐"},        {"98", "爵士乐"},       {"1117", "电子音乐"}, {"282", "节奏布鲁斯"}};

static const std::unordered_map<std::string, std::string> kGenreIdNeedCorss = {{"1134", "ACG"},  {"352", "摇滚"},      {"15", "流行"},
                                                                               {"119", "说唱"},  {"150", "民谣"},      {"576", "轻音乐"},
                                                                               {"98", "爵士乐"}, {"1117", "电子音乐"}, {"282", "节奏布鲁斯"}};

static const std::unordered_map<std::string, std::string> kGenreLanguaWhiteMap = {{"0", "国语"}, {"1", "粤语"}, {"2", "台语"}, {"3", "日语"},
                                                                                  {"4", "韩语"}, {"5", "英语"}, {"7", "其他"}, {"9", "纯音乐"}};

static const std::unordered_map<std::string, std::unordered_map<std::string, float>> kScenarioMapGenreScore = {
    {"2", {{"0_15", 1.0}, {"0_150", 1.0}, {"0_119", 1.0}, {"0_352", 1.0}, {"1_15", 1.0},  {"2194", 1.0},  {"1143", 1.0},
           {"5_15", 1.0}, {"5_119", 1.0}, {"5_352", 1.0}, {"5_98", 1.0},  {"5_282", 1.0}, {"3_15", 1.0},  {"3_1134", 1.0},
           {"4_15", 1.0}, {"4_119", 1.0}, {"1964", 1.0},  {"9_576", 1.0}, {"29", 1.0},    {"0_282", 1.0}, {"3_119", 1.0}}},
    {"3", {{"0_15", 1.0}, {"0_150", 1.0}, {"0_119", 1.0}, {"0_352", 1.0}, {"1_15", 1.0},  {"2194", 1.0},  {"1143", 1.0},
           {"5_15", 1.0}, {"5_119", 1.0}, {"5_352", 1.0}, {"5_98", 1.0},  {"5_282", 1.0}, {"3_15", 1.0},  {"3_1134", 1.0},
           {"4_15", 1.0}, {"4_119", 1.0}, {"1964", 1.0},  {"9_576", 1.0}, {"29", 1.0},    {"0_282", 1.0}, {"3_119", 1.0}}},
    {"4", {{"0_15", 1.0}, {"0_150", 1.0}, {"0_119", 1.0}, {"0_352", 1.0}, {"1_15", 1.0},  {"2194", 1.0},  {"1143", 1.0},
           {"5_15", 1.0}, {"5_119", 1.0}, {"5_352", 1.0}, {"5_98", 1.0},  {"5_282", 1.0}, {"3_15", 1.0},  {"3_1134", 1.0},
           {"4_15", 1.0}, {"4_119", 1.0}, {"1964", 1.0},  {"9_576", 1.0}, {"29", 1.0},    {"0_282", 1.0}, {"3_119", 1.0}}},
    {"5", {{"0_15", 1.0}, {"0_150", 1.0}, {"0_119", 1.0}, {"0_352", 1.0}, {"1_15", 1.0},  {"2194", 1.0},  {"1143", 1.0},
           {"5_15", 1.0}, {"5_119", 1.0}, {"5_352", 1.0}, {"5_98", 1.0},  {"5_282", 1.0}, {"3_15", 1.0},  {"3_1134", 1.0},
           {"4_15", 1.0}, {"4_119", 1.0}, {"1964", 1.0},  {"9_576", 1.0}, {"29", 1.0},    {"0_282", 1.0}, {"3_119", 1.0}}},
    {"6", {{"0_15", 1.0}, {"0_150", 1.0}, {"0_119", 1.0}, {"0_352", 1.0}, {"1_15", 1.0},  {"2194", 1.0},  {"1143", 1.0},
           {"5_15", 1.0}, {"5_119", 1.0}, {"5_352", 1.0}, {"5_98", 1.0},  {"5_282", 1.0}, {"3_15", 1.0},  {"3_1134", 1.0},
           {"4_15", 1.0}, {"4_119", 1.0}, {"1964", 1.0},  {"9_576", 1.0}, {"29", 1.0},    {"0_282", 1.0}, {"3_119", 1.0}}},
    {"8", {{"0_15", 1.0}, {"0_150", 1.0}, {"0_119", 1.0}, {"0_352", 1.0}, {"1_15", 1.0},  {"2194", 1.0},  {"1143", 1.0},
           {"5_15", 1.0}, {"5_119", 1.0}, {"5_352", 1.0}, {"5_98", 1.0},  {"5_282", 1.0}, {"3_15", 1.0},  {"3_1134", 1.0},
           {"4_15", 1.0}, {"4_119", 1.0}, {"1964", 1.0},  {"9_576", 1.0}, {"29", 1.0},    {"0_282", 1.0}, {"3_119", 1.0}}},
    {"9", {{"0_15", 1.0}, {"0_150", 1.0}, {"0_119", 1.0}, {"0_352", 1.0}, {"1_15", 1.0},  {"2194", 1.0},  {"1143", 1.0},
           {"5_15", 1.0}, {"5_119", 1.0}, {"5_352", 1.0}, {"5_98", 1.0},  {"5_282", 1.0}, {"3_15", 1.0},  {"3_1134", 1.0},
           {"4_15", 1.0}, {"4_119", 1.0}, {"1964", 1.0},  {"9_576", 1.0}, {"29", 1.0},    {"0_282", 1.0}, {"3_119", 1.0}}},
    {"10", {{"0_15", 1.0}, {"0_150", 1.0}, {"0_119", 1.0}, {"0_352", 1.0}, {"1_15", 1.0},  {"2194", 1.0},  {"1143", 1.0},
            {"5_15", 1.0}, {"5_119", 1.0}, {"5_352", 1.0}, {"5_98", 1.0},  {"5_282", 1.0}, {"3_15", 1.0},  {"3_1134", 1.0},
            {"4_15", 1.0}, {"4_119", 1.0}, {"1964", 1.0},  {"9_576", 1.0}, {"29", 1.0},    {"0_282", 1.0}, {"3_119", 1.0}}},
    {"11", {{"0_15", 1.0}, {"0_150", 1.0}, {"0_119", 1.0}, {"0_352", 1.0}, {"1_15", 1.0},  {"2194", 1.0},  {"1143", 1.0},
            {"5_15", 1.0}, {"5_119", 1.0}, {"5_352", 1.0}, {"5_98", 1.0},  {"5_282", 1.0}, {"3_15", 1.0},  {"3_1134", 1.0},
            {"4_15", 1.0}, {"4_119", 1.0}, {"1964", 1.0},  {"9_576", 1.0}, {"29", 1.0},    {"0_282", 1.0}, {"3_119", 1.0}}},
    {"12", {{"0_15", 1.0}, {"0_150", 1.0}, {"0_119", 1.0}, {"0_352", 1.0}, {"1_15", 1.0},  {"2194", 1.0},  {"1143", 1.0},
            {"5_15", 1.0}, {"5_119", 1.0}, {"5_352", 1.0}, {"5_98", 1.0},  {"5_282", 1.0}, {"3_15", 1.0},  {"3_1134", 1.0},
            {"4_15", 1.0}, {"4_119", 1.0}, {"1964", 1.0},  {"9_576", 1.0}, {"29", 1.0},    {"0_282", 1.0}, {"3_119", 1.0}}}};

// Note(lingjunxu) 因为曲库接口没语种，用地区大概猜一下吧
static const std::unordered_map<int, std::set<int> > kSingerArea2Language = {
    {0, {0, 1}}, // 港台->国语、粤语
    {1, {0}}, // 内地->国语
    {2, {3, 4}}, // 日韩->日语、韩语
    {3, {5}} // 欧美->英语
};

// 展示参数控制
static const int kDisplayMaxTrackNameSize = 30;
static const int kDisplayYearSongMaxTrackNameSize = 21;
static const std::string kDisplayBlockDesc = "单曲模块描述";
static const std::string kDisplayBlockLastDesc = "流派单曲降级";
static const std::string kDisplayBlockPremDesc = "个性化精选池单曲";
static const std::string kDisplayBlockSongDesc = "个性化场景单曲";
static const std::string kDisplayBlockGenreDesc = "兜底场景单曲";
static const std::string kDisplayBlockTimeDesc = "个性化年代单曲";
static const std::string kDisplayBlockNewDesc = "个性化新歌";
static const std::string kDisplayBlockWeSingDesc = "个性化新K歌";
static const std::string kDisplayBlockProfileRejustDesc = "画像调节";
static const std::string kDisplayBlockVipLimitedDesc = "无免内容";
static const std::string kDisplayBlockVipFreeDesc = "免费模式弹窗";
static const std::string kDisplayBlockHotNewDesc = "新热兜底";
static const std::string kDisplayBlockYearDesc = "那年今日";
static const std::string kDisplayBlockNewUserDesc = "新用户经典精品类";
static const std::string kDisplayBlockNewUserVipDesc = "新用户免费感知类";
static const std::string kDisplayBlockReturnUserpDesc = "回流用户类";
static const std::string kDisplayBlockFavoSimDesc = "收藏资产相似类";
static const std::string kDisplayBlockRecentListenSimDesc = "最近常听资产相似类";

static const std::string kDisplayBlockDefaultTitle = "大家都在听";
static const std::string kDisplayBlockDefaultTitleForProfileRejust = "歌曲风格";
static const std::string kDisplayBlockDefaultTemplate = "{String}";
static const std::string kDisplayBlockNewTitle = "今日新歌推荐";
static const std::string kDisplayBlockNewTemplate = "{String}";
static const std::string kDisplayBlockWeSingTitle = "大家都在唱";
static const std::string kDisplayBlockProfileRejustTitle = "大家都在调";
// static const std::string kDisplayBlockVipLimitedTitle = "VIP歌曲限时免费听"; // 20241220阻断实验 去除
static const std::string kDisplayBlockVipTitle = "VIP专属歌曲推荐";
static const std::string kDisplayBlockVipFreeTitle = "免费模式邀你畅听VIP歌曲";
static const std::string kDisplayBlockHotNewTitle = "热门歌曲推荐";
static const std::string kDisplayBlockFreeModeVipTitle = "VIP歌曲推荐";
static const std::string kDisplayBlockFreeModeNewUserVipTitle = "新人入站必听";
static const std::string kDisplayBlockFavoSimTitle = "从你的红心歌曲开始探索";
static const std::string kDisplayBlockRecentListenSimTitle1 = "查收这些你常听但未收藏的歌曲";
static const std::string kDisplayBlockRecentListenSimTitle2 = "这些听过的歌曲一定值得收藏";
static const std::string kDisplayBlockRecentListenSimTitle3 = "这些反复听过的歌一定值得收藏";
static const std::string kDisplayBlockWeSingTemplate = "{String}";
static const std::string kDisplayBlockProfileRejustTemplate = "{String}";
static const std::string kDisplayBlockVipLimitedTemplate = "{String}";
static const std::string kDisplayBlockVipFreeTemplate = "{String}";
static const std::string kDisplayBlockHotNewTemplate = "{String}";
static const std::string kDisplayBlockNewUserTemplate = "{String}";
static const std::string kDisplayBlockReturnUserTemplate = "{String}";
static const std::string kDisplayBlockNewProfileRejustTemplate = "根据你调节的「{String}」推荐";
static const std::string kDisplayBlockFavoSimTemplate = "{String}";

// 站外素材插入
static const std::string kDisplayBlockInsertMaterialFreeModeTitle = "新人入站必听";
static const std::string kDisplayBlockInsertMaterialYuanMengTitle = "元梦人都爱听的歌";
static const std::string kDisplayBlockInsertMaterialGaoDeTitle = "这些歌曲，找回你在路上的回忆";

// 追打模块
enum FollowUpType {
  FOLLOW_UP_EMPTY = 0,
  FOLLOW_UP_FAV = 1,         // 收藏
  FOLLOW_UP_CLICK_PLAY = 2,  // 点播
};
static const std::unordered_set<std::string> kTemplateFollowUpFavSet = {"收藏了「{String}」的还在听"};
static const std::unordered_set<std::string> kTemplateFollowUpClickPlaySet = {"听「{String}」的也喜欢"};

static const std::unordered_set<std::string> kTemplateRandomSet = {"值得红心的宝藏歌曲", "红心歌曲预定", "心动的歌曲推荐"};
static const std::unordered_set<std::string> kTemplateRandomSetForSong = {"心动的歌曲推荐"};
static const std::unordered_set<std::string> kTemplateRandomSetForSinger = {"值得红心的宝藏歌曲", "红心歌曲预定"};
static const std::unordered_set<std::string> kTemplateSongSet = {"听「{String}」的也在听"};
static const std::unordered_set<std::string> kTemplateSingerSet = {"听「{String}」的也在听"};
static const std::unordered_set<std::string> kTemplateGenreSet = {"值得单曲循环的{String}", "发掘更多好听的{String}"};
static const std::unordered_set<std::string> kTemplateDefaultSet = {"大家都在听"};
static const std::unordered_set<std::string> kTemplateYearSongSet = {"去年今日你在听「{String}」"};
// static const std::string kTemplateSearchFree = "限时免费听";
static const std::unordered_set<std::string> kTemplateNewUserRandomSet = {"新人必听 大家都在听的新热", "新人入站必听 精选热门曲库",
                                                                          "新人入站必听 曲库好歌精选"};
// static const std::unordered_set<std::string> kTemplateNewUserVipRandomSet = {"免费VIP精选  新人必听", "限时免费好歌 新人入站必听",
//                                                                              "新人福利 精选VIP免费听"};
static const std::unordered_set<std::string> kTemplateReturnUserRandomSet = {"重温曾经你最爱听的歌"};

static const std::unordered_set<std::string> kTemplaceBlackSetForSongSinger = {"大家都在听的宝藏单曲", "Q音必听经典歌曲", "猜你喜欢的音乐清单",
                                                                               "值得单曲循环的品质好歌", "今日必听的品质音乐"};

static const std::map<MusicProfileType, std::string> kTemplateProfileRejustMap = {
    {MusicProfileTypeSmall, "小众"},          {MusicProfileTypeHot, "热门"},           {MusicProfileTypeUnique, "独特品味限时供应"},
    {MusicProfileTypeMain, "主流旋律听个够"}, {MusicProfileTypeNew, "新歌FM的随时听"}, {MusicProfileTypeOld, "Pick一首怀旧金曲"},
};

static const std::map<std::string, std::string> kTemplateGenreProfileRejustMap = {
    {"0_15", "国语"}, {"5_15", "英语"}, {"4_15", "韩语"}, {"3_15", "日语"}, {"9_576", "纯音乐"}, {"1_15", "粤语"},
};

// 仅限中文+粤语
static const std::unordered_map<std::string, std::unordered_set<std::string>> kTemplateTimeChineseMap = {{"60", {"60年代的经典回忆"}},
                                                                                                         {"70", {"70年代点唱机：ON"}},
                                                                                                         {"80", {"重温80年代的音乐时光"}},
                                                                                                         {"90", {"时光机：下一站90年代"}},
                                                                                                         {"00", {"00年代的悦耳旋律"}}};
static const std::unordered_map<std::string, std::unordered_set<std::string>> kTemplateTimeGenreMap = {
    {"0_15", {"{String}年代的华语精选"}},
    {"0_150", {"{String}年代民谣回忆录"}},
    {"0_1134", {"高光时刻：{String}年代国语ACG"}},
    {"0_119", {"跟着这些{String}年代说唱燥起来"}},
    {"0_352", {"在这{String}年代里，摇滚吧！"}},
    {"1_15", {"陷入{String}年代的粤语旋律"}},
    {"5_119", {"回到最纯粹的{String}年代欧美说唱"}},
    {"5_98", {"醇享{String}年代爵士乐"}},
    {"5_352", {"重温{String}年代欧美摇滚经典"}},
    {"5_282", {"{String}年代欧美R&B"}},
    {"3_1134", {"回忆杀：{String}年代日语ACG"}},
    {"4_119", {"{String}年代韩式说唱，有点范儿"}},
    {"1964", {"在{String}年代电子舞曲中摇曳"}},
    {"9_576", {"倾听{String}年代轻音乐"}},
    {"0_282", {"律动的魅力：{String}年代国语R&B"}},
};
static const std::unordered_map<std::string, std::unordered_set<std::string>> kTemplateTimeLanguageMap = {
    {"5", {"难以忘怀的{String}年代欧美风"}},
    {"3", {"重拾日系{String}年代单曲"}},
    {"4", {"迷恋{String}年代韩圈流行"}},
};
static const std::unordered_map<std::string, std::string> kTemplateTimeLanguageWhiteMap = {
    {"0", "国语"}, {"1", "粤语"}, {"3", "日语"}, {"4", "韩语"}, {"5", "英语"}};
static const std::unordered_set<std::string> kTemplateTimeWhiteList = {"60", "70", "80", "90", "00"};

static const std::unordered_map<std::string, std::unordered_set<std::string>> kTemplateScenarioMap = {
    {"2", {"阳光照进来啦", "早啊，一切都会变好的", "早安歌来啦~"}},
    {"3", {"一天之计在于晨呐", "又是崭新的一天~", "来点音乐 创意涌现"}},
    {"4", {"正 午 时 分", "今日的午间音乐 请享用", "嗯 午安~"}},
    {"5", {"提升效率", "潜心创作必备", "学习工作加油站"}},
    {"6", {"晚风轻轻吹啊吹", "用音乐打发美好的时光吧", "夜幕降临 音乐应景"}},
    {"8", {"夜间音乐模式 ON", "开启今晚的音乐遨游", "最后一首啦 听完就睡"}},
    {"9", {"夜深了 来点音乐吧", "点一杯音乐伏特加", "还不睡么 听点歌吧"}},
    {"10", {"清晨时分 音乐懂你", "微凉的晨露", "梦醒时分"}},
    {"12", {"打破犯困节奏", "下午时分 音乐相伴", "音乐 下午茶 时光真好"}}};
static const std::unordered_map<std::string, std::unordered_set<std::string>> kTemplateGenreMap = {
    {"0_15", {"收获华语好心情", "值得一听的华语好歌", "不可多得的华语好声音", "华语点歌台", "华语好歌 百听不腻"}},
    {"0_150", {"今日的民谣时光", "远方的诗与这里的民谣"}},
    {"0_1134", {"爱听国漫的不要错过", "国漫原声尽在这里"}},
    {"0_119", {"国语嘻哈 燥一个", "中文说唱 这里报道"}},
    {"0_352", {"国语摇滚聚集地", "国语摇滚 揭开那块红布"}},
    {"1_15", {"粤听粤好听", "今日，你听咗未啊？"}},
    {"2194", {"饮一曲国风音乐", "开启国风之旅"}},
    {"1143", {"饮一曲国风音乐", "开启国风之旅", "国风音乐年代史"}},
    {"5_15", {"欧美音乐速递", "欧美音乐 一秒沦陷"}},
    {"5_119", {"英文说唱 活力全开", "欧美嘻哈模式 ON"}},
    {"5_352", {"欧美摇滚星球", "Rock&Roll!"}},
    {"5_98", {"品一杯爵士浓咖啡", "来点深蓝色爵士旋律"}},
    {"5_282", {"难以忘怀的欧美R&B", "尽享律动的欧美R&B"}},
    {"3_15", {"霓虹音乐风尚", "不可错过的日语歌曲"}},
    {"3_1134", {"宅圈必听 日系ACG", "好心情从日语ACG开启"}},
    {"4_15", {"精选韩流音乐", "韩流风向标"}},
    {"4_119", {"韩语嘻哈取向狙击", "韩语说唱宝藏"}},
    {"1964", {"调大音量 开启你的电音之旅", "电音控必备舞曲"}},
    {"9_576", {"解忧疗愈的轻音乐", "舒压轻音乐舱"}},
    {"29", {"优雅古典乐", "古典留声机"}},
    {"2198", {"熟悉的味道 熟悉的音乐", "无处不在的音乐节奏"}},
    {"0_282", {"聆听国语R&B的魅力", "一秒入魂的国语R&B"}},
    {"1142", {"听着DJ 摇起来", "根本停不下来的DJ"}},
    {"3_119", {"感受纯正的日式说唱", "这些日语说唱针不戳"}}};

static const std::map<MusicProfileType, std::unordered_set<std::string>> kLabelProfileRejustMap = {
    {MusicProfileTypeSmall, {"小众精选"}}, {MusicProfileTypeHot, {"热门好歌"}}, {MusicProfileTypeExplored, {"新品味探索"}},
    {MusicProfileTypeNew, {"新歌推荐"}},   {MusicProfileTypeOld, {"怀旧"}},     {MusicProfileTypeUnique, {"独特品味"}},
    {MusicProfileTypeMain, {"万人播放"}},
};

// 特征参数
static const std::string kFeatureSplitFS = ",";
static const std::string kFeatureSplitKV = "=";
static const std::string kFeatureSplitVS = "%";
static const int kFeatureFavoSongNum = 30;
static const int kFeatureSelfSongNum = 30;
static const int kFeatureDownSongNum = 30;
static const int kFeatureListSongNum = 30;
static const int kFeatureRadiSongNum = 10;
static const int kFeatureAiraSongNum = 10;
static const int kFeatureCutlSongNum = 15;
static const int kFeatureDustSingerNum = 10;
static const int kFeatureProfileSingerNum = 20;
static const int kFeatureProfileLangNum = 3;
static const int kFeatureProfileGenreNum = 10;
static const int kFeatureProfilePlaylistNum = 10;
static const int kFeatureQueryNum = 20;
static const int kFeatureCrossBatch = 20;

static const int kFeatureAttentionNum = 10;
static const int kFeatureFavoSongShortNum = 10;
static const int kFeatureSelfSongShortNum = 10;
static const int kFeatureDownSongShortNum = 10;
static const int kFeatureListSongShortNum = 10;
static const int kFeatureFavoSongLongNum = 10;
static const int kFeatureSelfSongLongNum = 10;
static const int kFeatureDownSongLongNum = 10;
static const int kFeatureListSongLongNum = 10;
static const int kFeatureFollowSingerNum = 8;
static const int kFeaRecoCompleteLen = 30;
static const int kFeaMinisterCompleteLen = 30;
static const int kFeaMinisterClickLen = 30;

// 点位历史
// define_feature(u_minister_click_songs);
// define_feature(u_minister_complete_songs);
// define_feature(u_minister_click_singers);
// define_feature(u_minister_complete_singers);
// // 推荐历史
// define_feature(u_reco_complete_songs);
// define_feature(u_reco_complete_singers);
// // 大盘历史
// define_feature(u_favo_songs);
// define_feature(u_favo_singers);
// define_feature(u_self_songs);
// define_feature(u_self_singers);
// define_feature(u_down_songs);
// define_feature(u_down_singers);
// define_feature(u_list_songs);
// define_feature(u_list_singers);
// define_feature(u_radi_songs);
// define_feature(u_radi_singers);
// define_feature(u_aira_songs);
// define_feature(u_aira_singers);
// define_feature(u_cutl_songs);
// define_feature(u_cutl_singers);
// define_feature(u_dust_singers);
// define_feature(u_prof_langs);
// define_feature(u_prof_singers);
// define_feature(u_prof_genres);
// define_feature(u_demo_sex);
// define_feature(u_demo_age);
// define_feature(u_demo_city);
// define_feature(u_demo_degree);
// define_feature(u_uin);
// define_feature(u_hour);
// define_feature(u_prof_playlist);
// define_feature(u_query_keys);
// define_feature(u_favo_len);
// define_feature(u_self_len);
// define_feature(u_list_len);
// define_feature(u_list_30_len);
// define_feature(u_list_reco_30_len);
// define_feature(u_favo_30_len);
// define_feature(u_self_30_len);
// define_feature(u_ct);
// define_feature(u_follow_singer);
// define_feature(u_short_favo_new_genre);
// define_feature(u_short_self_new_genre);
// define_feature(u_short_down_new_genre);
// define_feature(u_short_list_new_genre);
// define_feature(u_att_favo_self_songs_len);    // 用于Attention中的Mask
// define_feature(u_att_list_songs_len);         // 用于Attention中的Mask
// define_feature(u_att_favo_self_singers_len);  // 用于Attention中的Mask
// define_feature(u_att_list_singers_len);       // 用于Attention中的Mask
// define_feature(u_att_favo_self_songs_0);
// define_feature(u_att_favo_self_songs_1);
// define_feature(u_att_favo_self_songs_2);
// define_feature(u_att_favo_self_songs_3);
// define_feature(u_att_favo_self_songs_4);
// define_feature(u_att_favo_self_songs_5);
// define_feature(u_att_favo_self_songs_6);
// define_feature(u_att_favo_self_songs_7);
// define_feature(u_att_favo_self_songs_8);
// define_feature(u_att_favo_self_songs_9);
// define_feature(u_att_favo_self_singers_0);
// define_feature(u_att_favo_self_singers_1);
// define_feature(u_att_favo_self_singers_2);
// define_feature(u_att_favo_self_singers_3);
// define_feature(u_att_favo_self_singers_4);
// define_feature(u_att_favo_self_singers_5);
// define_feature(u_att_favo_self_singers_6);
// define_feature(u_att_favo_self_singers_7);
// define_feature(u_att_favo_self_singers_8);
// define_feature(u_att_favo_self_singers_9);
// define_feature(u_att_list_songs_0);
// define_feature(u_att_list_songs_1);
// define_feature(u_att_list_songs_2);
// define_feature(u_att_list_songs_3);
// define_feature(u_att_list_songs_4);
// define_feature(u_att_list_songs_5);
// define_feature(u_att_list_songs_6);
// define_feature(u_att_list_songs_7);
// define_feature(u_att_list_songs_8);
// define_feature(u_att_list_songs_9);
// define_feature(u_att_list_singers_0);
// define_feature(u_att_list_singers_1);
// define_feature(u_att_list_singers_2);
// define_feature(u_att_list_singers_3);
// define_feature(u_att_list_singers_4);
// define_feature(u_att_list_singers_5);
// define_feature(u_att_list_singers_6);
// define_feature(u_att_list_singers_7);
// define_feature(u_att_list_singers_8);
// define_feature(u_att_list_singers_9);

define_feature(i_song_id);
define_feature(i_album_id);
define_feature(i_album_type);
define_feature(i_song_area);
define_feature(i_duration);
define_feature(i_genre);
define_feature(i_language);
define_feature(i_release);
define_feature(i_singer_id);
define_feature(i_singer_type);
define_feature(i_singer_grade);
define_feature(i_song_quality);
define_feature(i_song_name);
define_feature(i_singer_name);
define_feature(i_releases);
define_feature(i_recall_type);
define_feature(i_recall_keys);
define_feature(i_recall_source);
define_feature(i_basic_score);
define_feature(i_pay_score);
define_feature(i_play_cnt_score);
define_feature(i_full_play_score);
define_feature(i_young_score);
define_feature(i_favor_score);
define_feature(i_share_score);
define_feature(i_useract_score);
define_feature(i_song_contri_score);
define_feature(i_full_play_cnt);
define_feature(i_favor_cnt);
define_feature(i_share_cnt);
define_feature(i_play_cnt);
define_feature(i_search_cnt1);
define_feature(i_search_increase_score);
define_feature(i_new_genre);
define_feature(i_pos);
define_feature(u_i_singer_score);
define_feature(u_i_genre_score);
define_feature(u_i_lan_score);
define_feature(u_i_song_in_query);
define_feature(u_i_singer_in_query);
define_feature(u_i_self_avg_sim);
define_feature(u_i_self_max_sim);
define_feature(u_i_favo_avg_sim);
define_feature(u_i_favo_max_sim);
define_feature(u_i_list_avg_sim);
define_feature(u_i_list_max_sim);
define_feature(longtail_flow_diff);

// const static std::unordered_set<std::string> kFRUserFeatureNames = {
//     k_u_favo_songs_fea,
//     k_u_favo_singers_fea,
//     k_u_self_songs_fea,
//     k_u_self_singers_fea,
//     k_u_down_songs_fea,
//     k_u_down_singers_fea,
//     k_u_list_songs_fea,
//     k_u_list_singers_fea,
//     k_u_radi_songs_fea,
//     k_u_radi_singers_fea,
//     k_u_aira_songs_fea,
//     k_u_aira_singers_fea,
//     k_u_cutl_songs_fea,
//     k_u_cutl_singers_fea,
//     k_u_dust_singers_fea,
//     k_u_prof_langs_fea,
//     k_u_prof_singers_fea,
//     k_u_prof_genres_fea,
//     k_u_demo_sex_fea,
//     k_u_demo_age_fea,
//     k_u_demo_city_fea,
//     k_u_demo_degree_fea,
//     k_u_uin_fea,
//     k_u_hour_fea,
//     k_u_prof_playlist_fea,
//     k_u_query_keys_fea,
//     k_u_favo_len_fea,
//     k_u_self_len_fea,
//     k_u_list_len_fea,
//     k_u_list_30_len_fea,
//     k_u_favo_30_len_fea,
//     k_u_self_30_len_fea,
//     k_u_ct_fea,
//     // 202103新增
//     k_u_follow_singer_fea,
//     k_u_short_favo_new_genre_fea,
//     k_u_short_self_new_genre_fea,
//     k_u_short_down_new_genre_fea,
//     k_u_short_list_new_genre_fea,

//     k_u_att_favo_self_songs_len_fea,
//     k_u_att_list_songs_len_fea,
//     k_u_att_favo_self_singers_len_fea,
//     k_u_att_list_singers_len_fea,
//     k_u_att_favo_self_songs_0_fea,
//     k_u_att_favo_self_songs_1_fea,
//     k_u_att_favo_self_songs_2_fea,
//     k_u_att_favo_self_songs_3_fea,
//     k_u_att_favo_self_songs_4_fea,
//     k_u_att_favo_self_songs_5_fea,
//     k_u_att_favo_self_songs_6_fea,
//     k_u_att_favo_self_songs_7_fea,
//     k_u_att_favo_self_songs_8_fea,
//     k_u_att_favo_self_songs_9_fea,
//     k_u_att_favo_self_singers_0_fea,
//     k_u_att_favo_self_singers_1_fea,
//     k_u_att_favo_self_singers_2_fea,
//     k_u_att_favo_self_singers_3_fea,
//     k_u_att_favo_self_singers_4_fea,
//     k_u_att_favo_self_singers_5_fea,
//     k_u_att_favo_self_singers_6_fea,
//     k_u_att_favo_self_singers_7_fea,
//     k_u_att_favo_self_singers_8_fea,
//     k_u_att_favo_self_singers_9_fea,

//     k_u_att_list_songs_0_fea,
//     k_u_att_list_songs_1_fea,
//     k_u_att_list_songs_2_fea,
//     k_u_att_list_songs_3_fea,
//     k_u_att_list_songs_4_fea,
//     k_u_att_list_songs_5_fea,
//     k_u_att_list_songs_6_fea,
//     k_u_att_list_songs_7_fea,
//     k_u_att_list_songs_8_fea,
//     k_u_att_list_songs_9_fea,
//     k_u_att_list_singers_0_fea,
//     k_u_att_list_singers_1_fea,
//     k_u_att_list_singers_2_fea,
//     k_u_att_list_singers_3_fea,
//     k_u_att_list_singers_4_fea,
//     k_u_att_list_singers_5_fea,
//     k_u_att_list_singers_6_fea,
//     k_u_att_list_singers_7_fea,
//     k_u_att_list_singers_8_fea,
//     k_u_att_list_singers_9_fea,

//     // 20211122新增
//     k_u_minister_click_songs_fea,
//     k_u_minister_complete_songs_fea,
//     k_u_minister_click_singers_fea,
//     k_u_minister_complete_singers_fea,
//     k_u_reco_complete_songs_fea,
//     k_u_reco_complete_singers_fea,
// };  // namespace minister

// const static std::unordered_set<std::string> kFRItemFeatureNames = {
//     k_i_song_id_fea,
//     k_i_album_id_fea,
//     k_i_album_type_fea,
//     k_i_song_area_fea,
//     k_i_duration_fea,
//     k_i_genre_fea,
//     k_i_language_fea,
//     k_i_release_fea,
//     k_i_singer_id_fea,
//     k_i_singer_type_fea,
//     k_i_singer_grade_fea,
//     k_i_song_quality_fea,
//     k_u_i_singer_score_fea,
//     k_u_i_genre_score_fea,
//     k_u_i_lan_score_fea,
//     k_i_song_name_fea,
//     k_i_singer_name_fea,
//     k_i_recall_type_fea,
//     k_i_recall_keys_fea,
//     k_i_recall_source_fea,
//     k_i_basic_score_fea,
//     k_i_pay_score_fea,
//     k_i_play_cnt_score_fea,
//     k_i_full_play_score_fea,
//     k_i_young_score_fea,
//     k_i_favor_score_fea,
//     k_i_share_score_fea,
//     k_i_useract_score_fea,
//     k_i_song_contri_score_fea,
//     k_u_i_song_in_query_fea,
//     k_u_i_singer_in_query_fea,
//     k_i_releases_fea,
//     k_i_full_play_cnt_fea,
//     k_i_favor_cnt_fea,
//     k_i_share_cnt_fea,
//     k_i_play_cnt_fea,
//     k_i_search_cnt1_fea,
//     k_i_search_increase_score_fea,
//     // 202103新增
//     k_i_new_genre_fea,
// };

// 预定义model
define_model_para(source);
define_model_para(user_value); // 用户价值预估模型
define_model_para(rough);
define_model_para(distillrough);
define_model_para(tdmrough);
define_model_para(wdrough);
define_model_para(roughemb);
define_model_para(roughindex);
define_model_para(fine);
define_model_para(subfine);
define_model_para(huiyuan);
define_model_para(longtail);
define_model_para(longtail_flow); // 长尾跃迁模型
define_model_para(delay);
define_model_para(singer);
define_model_para(content);
define_model_para(roughcontent);
define_model_para(contrough);
define_model_para(quality);
define_model_para(rerank);
define_model_para(recallemb);
define_model_para(finetrigger);
define_rpc_para(eap);
define_model_para(roughlongtail);
// Note(karinesun) ctr任务分屏实验
define_model_para(fine_first);
define_model_para(fine_other);
define_model_para(user_recomp);
define_model_para(user_longtail);
define_model_para(content_recallemb);
define_model_para(content_recallgate);
define_model_para(content_recall);
// 粗排并行重复度模型
define_model_para(rough_dup);
// 临时粗排gate参数
define_model_para(rough_gate);

// counter 定义
define_counter(request_qps);
define_counter(request_fail_qps);
define_counter(request_succ_qps);
define_counter(recall_item_num);
define_counter(recall_filtered_item_num);
define_counter(recall_fail_num);

define_counter(rough_musiceye_genre_score);
define_counter(rough_unified_genre_score);
define_counter(rough_unified_singer_score);
define_counter(rough_unified_song_score);
define_counter(rough_musiceye_scenario_score);
define_counter(rough_unified_scenario_score);
define_counter(rough_unified_song_prem_score);
define_counter(rough_times_genre_score);
define_counter(rough_times_language_score);
define_counter(rough_swing_singer_score);
define_counter(rough_swing_song_score);
define_counter(rough_swing_song_prem_score);

define_counter(rough_musiceye_genre_ratio);
define_counter(rough_unified_genre_ratio);
define_counter(rough_unified_singer_ratio);
define_counter(rough_unified_song_ratio);
define_counter(rough_musiceye_scenario_ratio);
define_counter(rough_unified_scenario_ratio);
define_counter(rough_unified_song_prem_ratio);
define_counter(rough_interest_complete_cnt);
define_counter(rough_reco_candidate_cnt);
define_counter(rough_times_genre_ratio);
define_counter(rough_times_language_ratio);
define_counter(rough_swing_singer_ratio);
define_counter(rough_swing_song_ratio);
define_counter(rough_swing_song_prem_ratio);

define_counter(predict_rough_unified_singer_consist);
define_counter(predict_rough_unified_song_consist);
define_counter(predict_rough_unified_song_prem_consist);

define_counter(qps_without_profile);
define_counter(qps_without_rectabstream);
define_counter(qps_without_rectabstream_dev);
define_counter(qps_without_asset);
define_counter(qps_without_kk_asset);
define_counter(qps_without_kg_asset);
define_counter(qps_without_song);
define_counter(qps_without_singer);
define_counter(qps_use_deviceprofile);
define_counter(qps_use_outerprofile);
define_counter(qps_without_login);
define_counter(query_words_num);

define_counter(tf_all_num);
define_counter(tf_swap_num);
define_counter(tf_final_num);

define_counter(reco_ir_item_num);
define_counter(reco_rough_item_num);
define_counter(reco_rough_dpa_item_num);
define_counter(reco_fr_item_num);
define_counter(reco_mr_item_num);
define_counter(reco_diverse_item_num);
define_counter(reco_ir_cost);
define_counter(reco_rough_cost);
define_counter(reco_fr_cost);
define_counter(reco_mr_cost);
define_counter(reco_cost);
define_counter(reco_fr_interest_complete);
define_counter(reco_mr_interest_complete);
define_counter(reco_final_interest_complete);
define_counter(reco_fr_reco_candidate);
define_counter(reco_mr_reco_candidate);
define_counter(reco_final_reco_candidate);
static const std::unordered_map<string, uint32_t> kFeatureStr2SlotDSSM = {{"u_uin", 2},
                                                                          {"u_age", 3},
                                                                          {"u_sex", 4},
                                                                          {"u_degree", 5},
                                                                          {"u_city_level", 6},
                                                                          {"u_list_songs", 7},
                                                                          {"u_favo_songs", 8},
                                                                          {"final_his_full_language", 29},
                                                                          {"final_his_fav_language", 31},
                                                                          {"full_assets_long_seq", 32},
                                                                          {"full_profile_long_seq", 33},
                                                                          {"full_fav_long_seq", 34}};

static const std::unordered_map<string, uint32_t> kFeatureStr2SlotidDSSMV2 = {{"u_uin", 2},
                                                                              {"u_ct", 3},
                                                                              {"u_age", 4},
                                                                              {"u_sex", 5},
                                                                              {"u_degree", 6},
                                                                              {"u_city_level", 7},
                                                                              {"u_comp_singers", 8},
                                                                              {"u_favo_singers", 9},
                                                                              {"u_self_singers", 10},
                                                                              {"u_profile_singers", 11},
                                                                              {"u_comp_songs", 12},
                                                                              {"u_favo_songs", 13},
                                                                              {"u_self_songs", 14},
                                                                              {"u_profile_songs", 15},
                                                                              {"u_comp_lan_genres", 16},
                                                                              {"u_favo_lan_genres", 17},
                                                                              {"u_self_lan_genres", 18},
                                                                              {"u_profile_genres", 19},
                                                                              {"source_id", 30},
                                                                              {"u_comp_timegaps", 40},
                                                                              {"u_favo_timegaps", 41},
                                                                              {"u_self_timegaps", 42},
                                                                              {"source_language", 43}};

static const unordered_map<string, int> kFeatureStr2SlotidPinner = {{"hist_song", 7}, {"hist_timegap", 8}, {"hist_type", 9}};

static const std::unordered_map<string, uint32_t> kFeatureStr2SlotidQKSeq = {{"u_qimei36", 2},
                                                                             {"u_qm_comp_songs", 3},
                                                                             {"u_qm_favo_songs", 4},
                                                                             {"u_qm_shar_songs", 5},
                                                                             {"u_qm_comp_timegaps", 6},
                                                                             {"u_qm_favo_timegaps", 7},
                                                                             {"u_qm_shar_timegaps", 8},
                                                                             {"u_kugou_comp_songs", 9},
                                                                             {"u_kugou_favo_songs", 10},
                                                                             {"u_kugou_shar_songs", 11},
                                                                             {"u_kugou_comp_timegaps", 12},
                                                                             {"u_kugou_favo_timegaps", 13},
                                                                             {"u_kugou_shar_timegaps", 14},
                                                                             {"u_qm_scene", 15},
                                                                             {"u_kugou_scene", 16}};

static const std::unordered_map<string, uint32_t> kFeatureStr2SlotDSSMNew = {{"u_uin", 2},
                                                         {"u_age", 3},
                                                         {"u_sex", 4},
                                                         {"u_degree", 5},
                                                         {"u_city_level", 6},
                                                         {"final_his_full", 7},
                                                         {"final_his_fav", 8},
                                                         {"final_his_full_language", 29},
                                                         {"final_his_fav_language", 31},
                                                         {"full_assets_long_seq", 32},
                                                         {"full_profile_long_seq", 33},
                                                         {"full_fav_long_seq", 34},
                                                         {"play_songs_list", 35},
                                                         {"play_language_list", 37}};

static const std::unordered_map<string, uint32_t> kFeatureStr2SlotidQKSeqSasRec = {
    {"u_qimei36", 2}, {"u_seq_songs", 3}, {"u_seq_timegaps", 4}, {"u_seq_scenes", 5}, {"u_seq_types", 6}};

// 标签服务
static const int minister_rectag_bid = 5;

// 标题替换检测item个数
static const int replace_check_nums = 3;
static const int set_song_template_type = 3;
static const int set_singer_template_type = 4;

// 同歌组相关bid
static const int kSameSongGroupBid = 2;             // quku接口全部同歌组
static const int kSameSongGroupCoverBid = 3;        // quku接口coverid
static const int kSameSongGroupPlatformBatch = 64;  // 调用特征平台batch
static const int kBidSameSongGroupLvl3 = 812;
static const int kBidSameSongGroupLvl4 = 813;

// 相似模块相关控制参数
static const int kRecallSourceMaxLenForSimTrack = 150; 
static const int kRecallSourceSingerMaxLenForSimTrack = 5;

// 全链路trace VIP用户（面向老板加trace lol）
static const std::vector<uint64_t> full_trace_vip_users = {80000967,
                                                           1152921504606847045,
                                                           12479883,
                                                           178301441,
                                                           41686823,
                                                           418256532,
                                                           1152921504633169781,
                                                           857432358,
                                                           326221988,
                                                           719287897,
                                                           57940340,
                                                           59837098,
                                                           14562404,
                                                           5153657,
                                                           55016026,
                                                           568849044,
                                                           124274503,
                                                           1191464006,
                                                           284197994,
                                                           530350923,
                                                           17017746,
                                                           529672605,
                                                           408587614,
                                                           799604,
                                                           1019786791,
                                                           350732972,
                                                           1020989782,
                                                           526608493,
                                                           9123632,
                                                           675151948,
                                                           443191416,
                                                           1152921504619181259,
                                                           996553127,
                                                           123531326,
                                                           517753466,
                                                           1152921504606847035,
                                                           497289346,
                                                           719225313,
                                                           85254383,
                                                           827438403,
                                                           1060873823,
                                                           937453681,
                                                           1579286505,
                                                           724112764,
                                                           215775576,
                                                           949598514,
                                                           469110004,
                                                           360409945,
                                                           326786272,
                                                           1152921504606847053,
                                                           862790577,
                                                           34424349,
                                                           664568075,
                                                           1363062691,
                                                           1171516704,
                                                           261045585,
                                                           170426866,
                                                           261035894,
                                                           343610916,
                                                           21931842,
                                                           996553127,
                                                           690961900,
                                                           253414855,
                                                           1152921504613724438,
                                                           3382579,
                                                           253414855,
                                                           1029828759,
                                                           123531326,
                                                           178301441,
                                                           182048286,
                                                           1311512866,
                                                           344854169,
                                                           4264119,
                                                           574541259,
                                                           258883314,
                                                           123019007,
                                                           275493960,
                                                           315282945,
                                                           447375278,
                                                           954490981,
                                                           541383711,
                                                           344854169,
                                                           308600021,
                                                           337361904,
                                                           1625636621,
                                                           1800002530,
                                                           674764641,
                                                           627287501,
                                                           326786272,
                                                           1052424836,
                                                           594859753,
                                                           277864058,
                                                           1152921504752146593,
                                                           965053119,
                                                           553299861,
                                                           1414018365,
                                                           315653373,
                                                           1152921504606847000,
                                                           517753466,
                                                           840821163,
                                                           279416961,
                                                           443191416,
                                                           674015144,
                                                           782512732,
                                                           21045872,
                                                           414568464,
                                                           497289346,
                                                           719225313,
                                                           909073746,
                                                           85254383,
                                                           827438403,
                                                           1060873823,
                                                           1171516704,
                                                           1243358295,
                                                           937453681,
                                                           724112764,
                                                           250608820,
                                                           1152921504617451662,
                                                           1191454006,
                                                           532649174,
                                                           52719099,
                                                           767065521,
                                                           1152921504777368783,
                                                           783881417,
                                                           1753105417,
                                                           350732972,
                                                           1020989782,
                                                           526608493,
                                                           1152921504619181259,
                                                           719225313,
                                                           794188845,
                                                           77215755,
                                                           453589103,
                                                           465792972,
                                                           57940340,
                                                           1191464006,
                                                           34424349,
                                                           493869956,
                                                           594859753,
                                                           1152921504633300159,
                                                           1152921504617451662,
                                                           406185057,
                                                           532649174,
                                                           1152921505030726810,
                                                           1152921504752146593,
                                                           1800002530,
                                                           631565822,
                                                           639936695,
                                                           985140164,
                                                           1152921504808086141,
                                                           411973532,
                                                           840821163,
                                                           6582496,
                                                           992990744,
                                                           1152921504864541379,
                                                           284197994,
                                                           1152921504712207099,
                                                           690961900,
                                                           1872102572,
                                                           9225383,
                                                           380746769,
                                                           1152921504614502568,
                                                           996553127,
                                                           3345952895,
                                                           3297838675,
                                                           1152921504673215468,
                                                           1152921504613724438,
                                                           1152921504811099521,
                                                           250608820,
                                                           326221988,
                                                           164204954,
                                                           1789253963,
                                                           24528743,
                                                           12479883,
                                                           421397097,
                                                           1152921505197201961,
                                                           66942332,
                                                           574541259,
                                                           1152921504897032067,
                                                           756411904,
                                                           1152921504728311330,
                                                           1152921505121071888,
                                                           1152921504704077790,
                                                           673769356,
                                                           495261620};

// 用户价值分对应用户级别
static const std::map<double, int, std::greater<double>> userValueLevelMap = {
    {0.8, 4}, 
    {0.6, 3},
    {0.5, 2},
    {0.4, 1},
    {0.1, 0},
    {0.06, 101},
    {0.04, 102},
    {0.02, 103},
    {0.0, 104} 
};
// 用户级别对应用户权重
static const std::unordered_map<int, float> userValueWeightMap = {
    {4, 1.8}, 
    {3, 1.6},
    {2, 1.4},
    {1, 1.2},
    {0, 1.0},
    {101, 0.8},
    {102, 0.7},
    {103, 0.6},
    {104, 0.5} 
};

// 臻品音质专区相关定义
static const std::string SoundQualityShelfName = "sound_quality";
static const std::string SoundQualityShelfPoolID = "1383";
static const std::string kDisplayBlockSoundQualityShelfTitle = "恭喜你！免费畅享30min母带沉浸音质";
static const std::string kDisplayBlockSoundQualityShelfTemplate = "{String}";

// 垂类专区相关定义
static const std::string VerticalShelfName = "vertical";
static const std::string kDisplayBlockVerticalEpopShelfTitle = "欧美控必听宝藏单曲";
static const std::string kDisplayBlockVerticalKpopShelfTitle = "韩流控必听宝藏单曲";
static const std::string kDisplayBlockVerticalJpopShelfTitle = "日文控必听宝藏单曲";
static const std::string kDisplayBlockVerticalShelfTemplate = "{String}";
}  // namespace minister
}  // namespace ai