import pandas as pd
import numpy as np
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze_data(file_path):
    """
    加载并分析eplus使用人数数据
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path, encoding='utf-8')
        print(f"数据加载成功，共有 {len(df)} 条记录")
        print(f"数据列数: {len(df.columns)}")
        
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def analyze_by_dimensions(df):
    """
    按用户名、代码语言、小组列表、用户中心、中心列表等维度进行聚类去重统计
    """
    print("\n=== 数据分析结果 ===")
    
    # 1. 用户名维度分析 (活跃度)
    print("\n1.a 用户名维度统计 (活跃度):")
    user_activity_stats = df['用户名'].value_counts() # 这就是每个用户的活跃情况
    print(f"总用户数 (基于出现次数): {len(user_activity_stats)}")
    print(f"活跃用户TOP10:")
    print(user_activity_stats.head(10))
    
    # 1.b 用户详细信息统计 (去重后，包含用户中心、中心列表和小组)
    print("\n1.b 用户详细信息统计 (去重后):")
    user_details_map = {} # 用户名 -> {'user_centers': set(), 'center_lists': set(), 'groups': set()}

    required_cols = ['用户名', '用户中心', '中心列表', '小组列表']
    for col in required_cols:
        if col not in df.columns:
            print(f"警告: 列 '{col}' 不在DataFrame中。相关分析可能不完整或不准确。")
            if col not in df.columns:
                 df[col] = pd.Series(dtype='object')

    for index, row in df.iterrows():
        user = row.get('用户名')
        user_center_val = row.get('用户中心')
        center_list_val = row.get('中心列表')
        group_list_str = row.get('小组列表')

        if pd.notna(user):
            user_key = str(user).strip()
            if not user_key:
                continue

            if user_key not in user_details_map:
                user_details_map[user_key] = {'user_centers': set(), 'center_lists': set(), 'groups': set()}

            if pd.notna(user_center_val) and str(user_center_val).strip():
                user_centers_in_row = [uc.strip() for uc in str(user_center_val).split(';') if uc.strip()]
                user_details_map[user_key]['user_centers'].update(user_centers_in_row)

            if pd.notna(center_list_val) and str(center_list_val).strip():
                center_lists_in_row = [cl.strip() for cl in str(center_list_val).split(';') if cl.strip()]
                user_details_map[user_key]['center_lists'].update(center_lists_in_row)

            if pd.notna(group_list_str) and str(group_list_str).strip():
                groups_in_row = [g.strip() for g in str(group_list_str).split(';') if g.strip()]
                user_details_map[user_key]['groups'].update(groups_in_row)

    print(f"独立用户总数 (有详细信息): {len(user_details_map)}")
    print("用户及其关联信息 (部分示例，完整列表将保存到文件):")
    count = 0
    # 为了示例打印，我们可以结合活跃度
    # 创建一个临时列表用于排序打印示例
    temp_user_list_for_print = []
    for user, details in user_details_map.items():
        activity = user_activity_stats.get(user, 0) # 获取活跃度
        temp_user_list_for_print.append({'user': user, 'details': details, 'activity': activity})

    # 按活跃度排序打印示例
    temp_user_list_for_print.sort(key=lambda x: x['activity'], reverse=True)

    for item in temp_user_list_for_print:
        user = item['user']
        details = item['details']
        activity = item['activity']

        sorted_user_centers = sorted(list(details['user_centers'])) if details['user_centers'] else ['无明确用户中心']
        sorted_groups = sorted(list(details['groups'])) if details['groups'] else ['无明确小组']
        user_centers_str = ";".join(sorted_user_centers)
        groups_str = ";".join(sorted_groups)
        print(f"  用户: {user}, 活跃度: {activity}, 用户中心: {user_centers_str}, 关联小组: {groups_str}")
        count += 1
        if count >= 5:
            if len(user_details_map) > 5:
                print("  ... (更多用户数据见 analysis_summary.txt 和 user_details.csv)")
            break

    # 2. 代码语言维度分析
    print("\n2. 代码语言维度统计:")
    # 处理可能的空值和多语言情况
    language_data = df['代码语言'].dropna()
    # 如果代码语言字段包含多个语言（用分隔符分隔），需要拆分
    all_languages = []
    for lang in language_data:
        if pd.notna(lang):
            # 假设多个语言用逗号或分号分隔
            langs = str(lang).replace(';', ',').split(',')
            all_languages.extend([l.strip() for l in langs if l.strip()])
    
    language_stats = Counter(all_languages)
    print(f"使用的编程语言种类: {len(language_stats)}")
    print("编程语言使用频次TOP10:")
    for lang, count in language_stats.most_common(10):
        print(f"  {lang}: {count}")
    
    # 3. 小组列表维度分析
    print("\n3. 小组列表维度统计:")
    group_data = df['小组列表'].dropna()
    all_groups = []
    for group in group_data:
        if pd.notna(group):
            # 假设多个小组用分号分隔
            groups = str(group).split(';')
            all_groups.extend([g.strip() for g in groups if g.strip()])
    
    group_stats = Counter(all_groups)
    print(f"涉及小组数量: {len(group_stats)}")
    print("小组参与度TOP10:")
    for group, count in group_stats.most_common(10):
        print(f"  {group}: {count}")
    
    # 4. 三维度交叉分析
    print("\n4. 三维度交叉统计:")
    
    # 去重统计：按用户名、代码语言、小组的组合去重
    unique_combinations = df[['用户名', '代码语言', '小组列表']].drop_duplicates()
    print(f"用户-语言-小组唯一组合数: {len(unique_combinations)}")
    
    # 用户在不同语言中的分布
    user_lang_combinations = df[['用户名', '代码语言']].drop_duplicates()
    user_lang_count = user_lang_combinations.groupby('用户名').size()
    print(f"平均每个用户使用的语言数: {user_lang_count.mean():.2f}")
    print(f"使用语言最多的用户使用了 {user_lang_count.max()} 种语言")
    
    # 用户在不同小组中的分布
    user_group_combinations = df[['用户名', '小组列表']].drop_duplicates()
    user_group_count = user_group_combinations.groupby('用户名').size()
    print(f"平均每个用户参与的小组数: {user_group_count.mean():.2f}")
    print(f"参与小组最多的用户参与了 {user_group_count.max()} 个小组")
    
    return {
        'user_activity_stats': user_activity_stats, # 包含每个用户的活跃度
        'user_details_map': user_details_map,
        'language_stats': language_stats,
        'group_stats': group_stats,
        'unique_combinations': unique_combinations,
        'user_lang_count': user_lang_count,
        'user_group_count': user_group_count
    }

def create_visualizations(analysis_results):
    """
    创建可视化图表
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('eplus使用人数数据分析可视化', fontsize=16)
    
    # 1. 用户活跃度分布
    user_stats = analysis_results['user_stats']
    axes[0, 0].bar(range(min(10, len(user_stats))), user_stats.head(10).values)
    axes[0, 0].set_title('TOP10活跃用户')
    axes[0, 0].set_xlabel('用户排名')
    axes[0, 0].set_ylabel('活跃次数')
    
    # 2. 编程语言使用分布
    language_stats = analysis_results['language_stats']
    top_languages = dict(language_stats.most_common(10))
    axes[0, 1].pie(top_languages.values(), labels=top_languages.keys(), autopct='%1.1f%%')
    axes[0, 1].set_title('TOP10编程语言分布')
    
    # 3. 用户语言使用数量分布
    user_lang_count = analysis_results['user_lang_count']
    axes[1, 0].hist(user_lang_count.values, bins=20, alpha=0.7)
    axes[1, 0].set_title('用户使用语言数量分布')
    axes[1, 0].set_xlabel('使用语言数量')
    axes[1, 0].set_ylabel('用户数量')
    
    # 4. 用户小组参与数量分布
    user_group_count = analysis_results['user_group_count']
    axes[1, 1].hist(user_group_count.values, bins=20, alpha=0.7, color='orange')
    axes[1, 1].set_title('用户参与小组数量分布')
    axes[1, 1].set_xlabel('参与小组数量')
    axes[1, 1].set_ylabel('用户数量')
    
    plt.tight_layout()
    plt.savefig('eplus_analysis_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """
    主函数
    """
    file_path = "eplus/使用人数-下钻明细D.csv"
    
    print("开始分析eplus使用人数数据...")
    
    df = load_and_analyze_data(file_path)
    if df is None:
        return
    
    print(f"\n数据概览:")
    print(f"数据形状: {df.shape}")
    print(f"主要列信息:")
    # 更新检查的列名
    key_columns = ['用户名', '代码语言', '小组列表', '用户中心', '中心列表']
    for col in key_columns:
        if col in df.columns:
            non_null_count = df[col].notna().sum()
            print(f"  {col}: {non_null_count}/{len(df)} 条有效记录")
        else:
            print(f"  列 '{col}' 不存在于CSV文件中。")

    analysis_results = analyze_by_dimensions(df)

    try:
        create_visualizations(analysis_results)
        print("\n可视化图表已保存为 'eplus_analysis_results.png'")
    except Exception as e:
        print(f"可视化生成失败: {e}")
    
    # 保存分析结果到 TXT 文件
    try:
        with open('analysis_summary.txt', 'w', encoding='utf-8') as f:
            f.write("eplus使用人数数据分析摘要\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"总记录数: {len(df)}\n")
            f.write(f"唯一用户数 (基于活跃度统计): {len(analysis_results['user_activity_stats'])}\n")
            f.write(f"独立用户总数 (有详细信息): {len(analysis_results['user_details_map'])}\n")
            f.write(f"编程语言种类: {len(analysis_results['language_stats'])}\n")
            f.write(f"涉及小组数 (基于小组列表统计): {len(analysis_results['group_stats'])}\n")
            f.write("\n" + "=" * 30 + "\n")
            f.write("用户详细信息 (按活跃度降序排列):\n") # 更新标题
            f.write("=" * 30 + "\n")

            # 为了在txt中也按活跃度排序，我们准备一个排序后的列表
            user_details_for_txt = []
            if analysis_results['user_details_map']:
                for user, details in analysis_results['user_details_map'].items():
                    activity = analysis_results['user_activity_stats'].get(user, 0)
                    user_details_for_txt.append({
                        'user': user,
                        'details': details,
                        'activity': activity
                    })
                user_details_for_txt.sort(key=lambda x: x['activity'], reverse=True)

                for item in user_details_for_txt:
                    user = item['user']
                    details = item['details']
                    activity = item['activity']
                    user_centers_str = ";".join(sorted(list(details['user_centers']))) if details['user_centers'] else "N/A"
                    center_lists_str = ";".join(sorted(list(details['center_lists']))) if details['center_lists'] else "N/A"
                    groups_str = ";".join(sorted(list(details['groups']))) if details['groups'] else "N/A"
                    f.write(f"  用户: {user} (活跃度: {activity})\n") # 添加活跃度
                    f.write(f"    用户中心: {user_centers_str}\n")
                    f.write(f"    中心列表: {center_lists_str}\n")
                    f.write(f"    关联小组: {groups_str}\n\n")
            else:
                f.write("  未找到用户详细信息。\n")
            f.write("\n")

        print("分析摘要及用户详细信息已保存为 'analysis_summary.txt'")
    except Exception as e:
        print(f"保存分析摘要到TXT失败: {e}")

    # 保存去重后的用户信息到 CSV 文件
    try:
        user_details_list_for_csv = []
        user_activity_stats = analysis_results['user_activity_stats'] # 获取活跃度统计

        for user, details in analysis_results['user_details_map'].items():
            activity_count = user_activity_stats.get(user, 0) # 获取该用户的活跃度
            user_centers_csv_str = ";".join(sorted(list(details['user_centers']))) if details['user_centers'] else ''
            groups_csv_str = ";".join(sorted(list(details['groups']))) if details['groups'] else ''

            user_details_list_for_csv.append({
                '用户名': user,
                '用户中心': user_centers_csv_str,
                '小组信息': groups_csv_str,
                '活跃情况': activity_count # 新增活跃情况列
            })

        if user_details_list_for_csv:
            user_df = pd.DataFrame(user_details_list_for_csv)
            # 按 "活跃情况" 降序排序，然后按 "用户名" 升序作为次要排序标准
            user_df = user_df.sort_values(by=['活跃情况', '用户名'], ascending=[False, True]).reset_index(drop=True)

            csv_file_path = 'user_details.csv'
            user_df.to_csv(csv_file_path, index=False, encoding='utf-8-sig')
            print(f"去重后的用户详细信息 (含活跃情况并排序) 已保存为 '{csv_file_path}'")
        else:
            print("没有用户详细信息可保存到CSV。")

    except Exception as e:
        print(f"保存用户详细信息到CSV失败: {e}")

    print("\n数据分析完成！")

if __name__ == "__main__":
    main()