# coding:utf-8
import os
import requests
import json


def test(url):
    forward_url = "http://video-qa-liveshow.tme.ec.woa.com/"
    #forward_url = "http://video-qa.tme.ec.woa.com/"
    #forward_url = "http://avr.kg.qq.com/video-qa/"
    #forward_url = "http://video-qa-liveshow.tme.ec.woa.com/"
    #forward_url = "http://10.101.140.103:8080"
    # url = 'https://media-center-1258344705.cos.ap-shanghai.myqcloud.com/clarity/1_0/81e464a5-701c-45a9-9660-73190dace2c1.mp4' #示例视频文件
    #url = 'http://fsmvpc.hw.kugou.com/202302021624/33c3946af7098d9f908fff0037aca5cd/KGTX/CLTX002/be12753121e46680a3745e4eafc30747.mp4' #示例图片文件
    data = {
        'url': url,
        'cmd': 'predict',  # or 'analysis' # or 'predict, analysis'
        'cmd': 'predict',
        'user': "mannaxu",
    }
    files = {
         #"file":open('./540p_20.mp4','rb') #若要采用直接传输模式，则上面url置空（'url':'')，这里直接传输二进制打开的本地文件
        # "file": open('./ac819a78-d555-4fca-b551-e60ff7c869e2.mp4','rb')
    }

    files = {}
    try:
        response = requests.request("GET", forward_url, data=data, files=files)
        print(url)
        print(response.text)
    except Exception as e:
        print(e)


def score(video_file):
    forward_url = "http://video-qa-liveshow.tme.ec.woa.com/"
    #forward_url = "http://10.101.140.103:8080"
    #forward_url = "http://video-qa.tme.ec.woa.com"
    data = {
        'url': "", #若采用url模式，则这里填写需要打分的文件的url地址
        'cmd': 'predict',  # or 'analysis' # or 'predict, analysis'
        'url': "",
        'cmd': 'predict',
        'user': "mannaxu",
    }
    

    files = {
         "file":open(video_file,'rb') #若要采用直接传输模式，则上面url置空（'url':'')，这里直接传输二进制打开的本地文件
        # "file": open('./ac819a78-d555-4fca-b551-e60ff7c869e2.mp4','rb')
         "file":open(video_file,'rb')
    }
    try:
        response = requests.request("GET", forward_url, data=data, files=files)
        print(response.text)
        return
    except Exception as e:
        print(e)

def get_tdqa_score(filepath, newtag=True):
    # forward_url = "http://video-qa.tme.ec.woa.com/"
    #forward_url = "http://avr.kg.qq.com/video-qa/"
    # forward_url = "http://134.175.137.179:8000/"
    #forward_url = "http://10.101.140.103:8080"
    forward_url = ""
    if newtag is True:
        forward_url = "http://10.101.140.103:8080"
    else:
        forward_url = "http://video-qa-liveshow.tme.ec.woa.com/"

    #print(forward_url)
    #forward_url = "http://video-qa.tme.ec.woa.com/"
    #forward_url = "http://avr.kg.qq.com/video-qa/"
    data = {
        'url': "",
        'cmd': 'predict',  # or 'analysis' # or 'predict, analysis'
        'cmd': 'predict',
        'user': "mannaxu",
    }
    files = {"file": open(filepath, 'rb')}
    try:
        response = requests.request("GET", forward_url, data=data, files=files)
        print(response.text)
        if response.status_code == 200:
            return float(json.loads(response.text).get("data").get("score"))
        else:
            return -1
    except Exception as e:
        print(e)
        return -1

if __name__ == '__main__':

    # urls = [
    #     "https://media-center-1258344705.cos.ap-shanghai.myqcloud.com/clarity/1_1/76d5fc14-ba17-4186-b6ed-b185cc9adf78.mp4",
    #     "https://media-center-1258344705.cos.ap-shanghai.myqcloud.com/clarity/1_1/e326334d-69f5-4d50-984c-0870ee2aaff0.mp4",
    #     "https://media-center-1258344705.cos.ap-shanghai.myqcloud.com/clarity/1_1/884b8656-a2fc-42b6-8569-c22b41d0f8d3.mp4",
    #     "https://media-center-1258344705.cos.ap-shanghai.myqcloud.com/clarity/1_1/59e84995-952a-494d-96b1-b3fb59519c78.mp4",
    #     "https://media-center-1258344705.cos.ap-shanghai.myqcloud.com/clarity/1_1/367fbfd5-e1a5-4d74-a20b-49ed1ce6ca63.mp4"
    # ]
    # #test(urls[0])
    # for url in urls:
    #     test(url)
    

    video_path = r"/Users/<USER>/Desktop/横屏样本素材" \
                 r"" \
                 r"/"
    fb = open("kugouhp_tdqa1.2.txt", "w+")
    # total = 0.0
    for root, dirs, files in os.walk(video_path):

        for fl in files:
            #if fl.endswith(".mp4"):
            fl_path = os.path.join(root,fl)
            #print(fl_path)
            #fb.write()

            #score(fl_path)
            score = get_tdqa_score(fl_path, True)

            print(fl_path + " " + str(score))
            #printstr = fl + " " + str(old_score) + " " + str(new_score)
            #print(fl + " old" + str(score))
            #print(printstr)

            # if score != -1:
            #     total = total + score
            #     count = count + 1

            
            fb.write(fl + ";" + str(score) + "\n")
            #else:
                #print "当前只支持mp4格式"
    # print("avg score: {}".format(float(total / count)))
    fb.close()

