<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>番茄钟APP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f5;
            font-family: 'Inter', sans-serif;
        }
        .phone-frame {
            width: 375px;
            height: 812px;
            border: 1px solid #e0e0e0;
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
    </style>
</head>
<body class="flex justify-center items-center min-h-screen p-8 gap-8">
    <!-- 计时器主页 -->
    <div class="phone-frame bg-gradient-to-br from-rose-50 to-rose-100">
        <div class="flex flex-col h-full p-8">
            <!-- 顶部导航 -->
            <div class="flex justify-between items-center mb-12">
                <h1 class="text-2xl font-bold text-black">番茄钟</h1>
                <button class="text-black">
                    <i class="ri-settings-3-line text-2xl"></i>
                </button>
            </div>
            
            <!-- 计时器区域 -->
            <div class="flex-1 flex flex-col items-center justify-center">
                <div class="relative w-64 h-64 flex items-center justify-center mb-8">
                    <!-- 圆形进度条 -->
                    <svg class="progress-ring absolute" width="256" height="256">
                        <circle class="text-rose-100" stroke="currentColor" stroke-width="8" fill="transparent" r="120" cx="128" cy="128" />
                        <circle class="text-rose-500" stroke="currentColor" stroke-width="8" fill="transparent" r="120" cx="128" cy="128" stroke-dasharray="754" stroke-dashoffset="188" />
                    </svg>
                    
                    <!-- 时间显示 -->
                    <div class="text-center z-10">
                        <h2 class="text-5xl font-bold text-black mb-2">25:00</h2>
                        <p class="text-black opacity-75">专注时间</p>
                    </div>
                </div>
                
                <!-- 控制按钮 -->
                <div class="flex gap-6 mb-8">
                    <button class="w-16 h-16 rounded-full bg-white shadow-md flex items-center justify-center">
                        <i class="ri-skip-back-line text-2xl text-black"></i>
                    </button>
                    <button class="w-20 h-20 rounded-full bg-gradient-to-r from-rose-400 to-rose-500 shadow-lg flex items-center justify-center">
                        <i class="ri-play-fill text-3xl text-white"></i>
                    </button>
                    <button class="w-16 h-16 rounded-full bg-white shadow-md flex items-center justify-center">
                        <i class="ri-skip-forward-line text-2xl text-black"></i>
                    </button>
                </div>
            </div>
            
            <!-- 底部统计 -->
            <div class="grid grid-cols-3 gap-4 mb-8">
                <div class="bg-white rounded-2xl p-4 shadow-sm text-center">
                    <p class="text-black text-xs opacity-75">今日专注</p>
                    <h3 class="text-black text-xl font-bold">2小时</h3>
                </div>
                <div class="bg-white rounded-2xl p-4 shadow-sm text-center">
                    <p class="text-black text-xs opacity-75">完成番茄</p>
                    <h3 class="text-black text-xl font-bold">4个</h3>
                </div>
                <div class="bg-white rounded-2xl p-4 shadow-sm text-center">
                    <p class="text-black text-xs opacity-75">专注效率</p>
                    <h3 class="text-black text-xl font-bold">85%</h3>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 设置页面 -->
    <div class="phone-frame bg-white">
        <div class="flex flex-col h-full p-8">
            <!-- 顶部导航 -->
            <div class="flex items-center mb-12">
                <button class="mr-4 text-black">
                    <i class="ri-arrow-left-line text-2xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-black">设置</h1>
            </div>
            
            <!-- 设置选项 -->
            <div class="flex-1">
                <!-- 时间设置 -->
                <div class="mb-8">
                    <h2 class="text-black text-lg font-semibold mb-4">时间设置</h2>
                    
                    <div class="bg-gray-50 rounded-2xl p-5 shadow-sm mb-4">
                        <div class="flex justify-between items-center mb-6">
                            <p class="text-black">专注时长</p>
                            <div class="flex items-center gap-4">
                                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                    <i class="ri-subtract-line text-black"></i>
                                </button>
                                <span class="text-black font-medium">25分钟</span>
                                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                    <i class="ri-add-line text-black"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <p class="text-black">休息时长</p>
                            <div class="flex items-center gap-4">
                                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                    <i class="ri-subtract-line text-black"></i>
                                </button>
                                <span class="text-black font-medium">5分钟</span>
                                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                    <i class="ri-add-line text-black"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 rounded-2xl p-5 shadow-sm">
                        <div class="flex justify-between items-center mb-6">
                            <p class="text-black">长休息时长</p>
                            <div class="flex items-center gap-4">
                                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                    <i class="ri-subtract-line text-black"></i>
                                </button>
                                <span class="text-black font-medium">15分钟</span>
                                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                    <i class="ri-add-line text-black"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <p class="text-black">长休息间隔</p>
                            <div class="flex items-center gap-4">
                                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                    <i class="ri-subtract-line text-black"></i>
                                </button>
                                <span class="text-black font-medium">4个番茄</span>
                                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                    <i class="ri-add-line text-black"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 其他设置 -->
                <div>
                    <h2 class="text-black text-lg font-semibold mb-4">通知设置</h2>
                    
                    <div class="bg-gray-50 rounded-2xl p-5 shadow-sm mb-4">
                        <div class="flex justify-between items-center">
                            <p class="text-black">声音提醒</p>
                            <div class="relative inline-block w-12 h-6 rounded-full bg-rose-400">
                                <div class="absolute right-1 top-1 w-4 h-4 rounded-full bg-white shadow-sm"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 rounded-2xl p-5 shadow-sm">
                        <div class="flex justify-between items-center">
                            <p class="text-black">振动提醒</p>
                            <div class="relative inline-block w-12 h-6 rounded-full bg-gray-200">
                                <div class="absolute left-1 top-1 w-4 h-4 rounded-full bg-white shadow-sm"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 保存按钮 -->
            <button class="w-full py-4 rounded-xl bg-gradient-to-r from-rose-400 to-rose-500 text-white font-medium shadow-md">
                保存设置
            </button>
        </div>
    </div>
</body>
</html> 