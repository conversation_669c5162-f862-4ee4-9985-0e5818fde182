# 登录表单组件

这是一个用于用户登录的React组件，提供了基本的用户认证表单界面。

## 功能特点

- 用户名和密码输入验证
- 登录状态加载指示
- 错误信息展示
- 响应式设计，适配各种屏幕尺寸
- 包含忘记密码和注册新账号的链接

## 使用方法

```tsx
import LoginForm from './components/LoginForm';

function App() {
  const handleLogin = (username: string, password: string) => {
    // 处理登录逻辑，例如API调用
    console.log('登录尝试:', username, password);
  };

  return (
    <div className="app">
      <LoginForm 
        onSubmit={handleLogin}
        loading={false} // 可选参数，控制加载状态
      />
    </div>
  );
}

export default App;
```

## 属性说明

| 属性 | 类型 | 是否必需 | 默认值 | 描述 |
|------|------|----------|--------|------|
| onSubmit | `(username: string, password: string) => void` | 是 | - | 登录表单提交时的回调函数 |
| loading | `boolean` | 否 | `false` | 控制表单是否处于加载状态 |

## 样式定制

组件使用CSS类名进行样式定义，可以通过覆盖以下CSS类来定制样式：

- `.login-form-container` - 表单容器
- `.login-form-card` - 表单卡片
- `.login-form-title` - 表单标题
- `.login-form-error` - 错误信息样式
- `.login-form-field` - 输入字段容器
- `.login-form-button` - 登录按钮
- `.login-form-links` - 链接区域
- `.login-form-link` - 链接样式

## 后续改进计划

- 添加记住密码功能
- 支持社交媒体登录
- 添加深色模式支持
- 国际化支持