.login-form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.login-form-card {
  width: 100%;
  max-width: 420px;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.login-form-title {
  font-size: 1.75rem;
  margin-bottom: 1.5rem;
  text-align: center;
  color: #333;
}

.login-form-error {
  background-color: #fdecec;
  color: #d32f2f;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.login-form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.login-form-field label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #555;
}

.login-form-field input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.login-form-field input:focus {
  outline: none;
  border-color: #2684ff;
  box-shadow: 0 0 0 1px #2684ff;
}

.login-form-field input::placeholder {
  color: #aaa;
}

.login-form-actions {
  margin-top: 0.5rem;
}

.login-form-button {
  width: 100%;
  padding: 0.75rem;
  background-color: #1976d2;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-form-button:hover {
  background-color: #1565c0;
}

.login-form-button:disabled {
  background-color: #90caf9;
  cursor: not-allowed;
}

.login-form-links {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
}

.login-form-link {
  color: #1976d2;
  font-size: 0.875rem;
  text-decoration: none;
}

.login-form-link:hover {
  text-decoration: underline;
}