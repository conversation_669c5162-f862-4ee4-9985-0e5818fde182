#!/usr/bin/env python3
# -*- coding: utf-8 -*-

class OptimizedSolution:
    """
    Distinct Subsequences 问题的最优解决方案
    
    问题：给定字符串 s 和 t，计算 s 中包含 t 作为子序列的不同方式数量
    
    时间复杂度：O(m×n)，其中 m = len(s), n = len(t)
    空间复杂度：O(n)
    """
    
    def numDistinct(self, s: str, t: str) -> int:
        """
        最优解：滚动数组 + 早期终止优化
        
        Args:
            s: 源字符串
            t: 目标子序列
            
        Returns:
            s 中包含 t 作为子序列的不同方式数量
        """
        m, n = len(s), len(t)
        
        # 早期终止条件
        if m < n:
            return 0
        if n == 0:
            return 1
        if m == 0:
            return 0
            
        # 字符频率检查 - 早期终止优化
        from collections import Counter
        s_count = Counter(s)
        t_count = Counter(t)
        
        for char, count in t_count.items():
            if s_count[char] < count:
                return 0
        
        # 滚动数组动态规划
        # dp[j] 表示当前行中 s[i:] 包含 t[j:] 的子序列数量
        dp = [0] * (n + 1)
        next_dp = [0] * (n + 1)
        dp[n] = 1  # 空字符串匹配空字符串有1种方式
        
        for i in range(m - 1, -1, -1):
            next_dp[n] = 1  # 每行都要重置边界条件
            
            for j in range(n - 1, -1, -1):
                if s[i] == t[j]:
                    # 字符匹配：可以选择使用或不使用当前字符
                    next_dp[j] = dp[j + 1] + dp[j]
                else:
                    # 字符不匹配：只能不使用当前字符
                    next_dp[j] = dp[j]
            
            # 交换数组，避免复制开销
            dp, next_dp = next_dp, dp
        
        return dp[0]
    
    def numDistinct_simple(self, s: str, t: str) -> int:
        """
        简化版本：适合面试使用
        
        逻辑清晰，容易理解和实现
        """
        m, n = len(s), len(t)
        
        if m < n:
            return 0
        
        # dp[j] 表示 s 的前 i 个字符中包含 t 的前 j 个字符的子序列数量
        dp = [0] * (n + 1)
        dp[0] = 1  # 空字符串匹配空字符串
        
        for i in range(1, m + 1):
            # 从右往左更新，避免状态覆盖
            for j in range(min(i, n), 0, -1):
                if s[i - 1] == t[j - 1]:
                    dp[j] = dp[j] + dp[j - 1]
        
        return dp[n]


def test_solutions():
    """测试所有解决方案"""
    solution = OptimizedSolution()
    
    test_cases = [
        ("rabbbit", "rabbit", 3),
        ("babgbag", "bag", 5),
        ("a", "a", 1),
        ("a", "b", 0),
        ("", "", 1),
        ("abc", "", 1),
        ("", "a", 0),
        ("aaa", "aa", 3),
        ("aab", "ab", 2),
    ]
    
    print("🧪 测试结果:")
    print("-" * 60)
    
    all_passed = True
    for s, t, expected in test_cases:
        result1 = solution.numDistinct(s, t)
        result2 = solution.numDistinct_simple(s, t)
        
        passed = result1 == expected and result2 == expected
        all_passed &= passed
        
        status = "✅" if passed else "❌"
        print(f"{status} s='{s}', t='{t}' -> 期望:{expected}, 最优解:{result1}, 简化版:{result2}")
    
    print("-" * 60)
    print(f"总体结果: {'🎉 全部通过!' if all_passed else '❌ 存在错误'}")
    
    # 性能测试
    print("\n⚡ 性能测试:")
    import time
    
    s_large = "a" * 1000
    t_large = "a" * 500
    
    start = time.time()
    result = solution.numDistinct(s_large, t_large)
    time_optimized = time.time() - start
    
    start = time.time()
    result_simple = solution.numDistinct_simple(s_large, t_large)
    time_simple = time.time() - start
    
    print(f"大数据测试 (s长度:{len(s_large)}, t长度:{len(t_large)}):")
    print(f"最优解: 结果={result}, 时间={time_optimized:.6f}秒")
    print(f"简化版: 结果={result_simple}, 时间={time_simple:.6f}秒")
    print(f"结果一致: {'✅' if result == result_simple else '❌'}")
    
    if time_simple > 0:
        speedup = time_simple / time_optimized
        print(f"性能提升: {speedup:.2f}x")


if __name__ == "__main__":
    test_solutions()