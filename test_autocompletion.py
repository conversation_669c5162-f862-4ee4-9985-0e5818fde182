#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import List
from collections import Counter


# 给定 n 个非负整数，用来表示柱状图中各个柱子的高度。每个柱子彼此相邻，且宽度为 1 。

# 求在该柱状图中，能够勾勒出来的矩形的最大面积。


class Solution:
    def largestRectangleArea(self, heights: List[int]) -> int:
        if not heights:
            return 0
        heights = [0] + heights + [0]
        stack = [0]
        res = 0
        for i in range(1, len(heights)):
            while heights[i] < heights[stack[-1]]:
                h = heights[stack.pop()]
                w = i - stack[-1] - 1
                res = max(res, h * w)
            stack.append(i)
            print(stack)
        return res

if __name__ == "__main__":
    s = Solution()
    print(s.largestRectangleArea([2,1,5,6,2,3]))
    print(s.largestRectangleArea([2,4]))
    print(s.largestRectangleArea([2,1,2]))