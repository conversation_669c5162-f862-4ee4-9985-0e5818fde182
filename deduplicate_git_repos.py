#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Git仓库地址去重工具
用于去除txt文件中重复的git仓库地址
"""

import sys
import os
from typing import List, Set


class GitRepoDeduplicator:
    """Git仓库地址去重器"""
    
    def __init__(self):
        self.seen_urls: Set[str] = set()
        self.url_mapping = {
            # 标准化URL映射，处理不同协议但相同仓库的情况
            '***************:': 'https://git.woa.com/',
            '**************:': 'https://github.com/',
            'git.woa.com:': 'https://git.woa.com/',
            'cnb.tmeoa.com/': 'https://cnb.tmeoa.com/',
            'http://': 'https://',
        }
    
    def normalize_url(self, url: str) -> str:
        """标准化URL以便比较"""
        url = url.strip()
        if not url or url == '/':
            return ''
        
        # 处理特殊情况
        normalized = url
        
        # 替换协议前缀
        for old_prefix, new_prefix in self.url_mapping.items():
            if normalized.startswith(old_prefix):
                normalized = normalized.replace(old_prefix, new_prefix, 1)
                break
        
        # 移除末尾的.git（如果有的话）
        if normalized.endswith('.git'):
            normalized = normalized[:-4]
        
        # 移除末尾的斜杠
        normalized = normalized.rstrip('/')
        
        return normalized
    
    def deduplicate_file(self, input_file: str, output_file: str = None) -> None:
        """对文件进行去重处理"""
        if not os.path.exists(input_file):
            print(f"错误：文件 {input_file} 不存在")
            return
        
        # 如果没有指定输出文件，则在原文件名基础上添加_deduplicated
        if output_file is None:
            base, ext = os.path.splitext(input_file)
            output_file = f"{base}_deduplicated{ext}"
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            unique_lines = []
            total_count = 0
            duplicate_count = 0
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                # 跳过空行和特殊标记
                if not line or line == '/':
                    continue
                
                total_count += 1
                
                # 标准化URL用于去重判断
                normalized = self.normalize_url(line)
                if not normalized:
                    continue
                
                if normalized not in self.seen_urls:
                    self.seen_urls.add(normalized)
                    unique_lines.append(line)
                else:
                    duplicate_count += 1
            
            # 写入去重后的结果
            with open(output_file, 'w', encoding='utf-8') as f:
                for line in unique_lines:
                    f.write(line + '\n')
            
            print(f"处理完成！")
            print(f"原始文件：{input_file}")
            print(f"输出文件：{output_file}")
            print(f"总行数：{total_count}")
            print(f"去重后行数：{len(unique_lines)}")
            print(f"去除重复：{duplicate_count}")
            
        except Exception as e:
            print(f"处理文件时出错：{e}")
    
    def get_statistics(self) -> dict:
        """获取去重统计信息"""
        return {
            'unique_urls': len(self.seen_urls),
            'normalized_urls': list(self.seen_urls)
        }


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法：")
        print("  python deduplicate_git_repos.py <输入文件> [输出文件]")
        print("示例：")
        print("  python deduplicate_git_repos.py light_ori.txt")
        print("  python deduplicate_git_repos.py light_ori.txt output.txt")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    deduplicator = GitRepoDeduplicator()
    deduplicator.deduplicate_file(input_file, output_file)


if __name__ == "__main__":
    main()