import unittest
from unittest.mock import patch, Mock, mock_open
from QualityGrade import QualityGrade
import os
import requests
from parameterized import parameterized


class TestQualityGrade(unittest.TestCase):

    def setUp(self):
        self.tester = QualityGrade()
        self.test_dir = "/fake/path"
        self.test_file = "/fake/path/test.mp4"

    def test_init_params(self):
        self.assertEqual(self.tester.headers, {'Content-type': 'application/json'})
        self.assertTrue('http' in self.tester.proxies)
        self.assertEqual(self.tester.requet_url,
                        "http://vc.y.qq.com/cgi-bin/musicu.fcg")

    @patch('requests.post')
    @parameterized.expand([
        (200, True),
        (404, False),
        (500, False)
    ])
    def test_get_video(self, status_code, expected, mock_post):
        mock_response = Mock()
        mock_response.status_code = status_code
        mock_post.return_value = mock_response

        response = self.tester.get_video(["show1", "show2"])
        mock_post.assert_called_once_with(
            self.tester.requet_url,
            json=Mock(),
            headers=self.tester.headers,
            proxies=self.tester.proxies
        )
        self.assertEqual(response.status_code == 200, expected)

    @patch('requests.post')
    @patch('builtins.open', new_callable=mock_open)
    @parameterized.expand([
        (200, True),
        (404, False),
        (500, False)
    ])
    def test_get_score(self, status_code, expected, mock_file, mock_post):
        mock_response = Mock()
        mock_response.status_code = status_code
        mock_post.return_value = mock_response

        response = self.tester.get_score(self.test_file)
        mock_file.assert_called_with(self.test_file, 'rb')
        mock_post.assert_called_once_with(
            "http://10.101.136.240:8081/uploadAnchorVideo/",
            files={'profile': mock_file.return_value.__enter__.return_value},
            proxies=self.tester.proxies
        )
        self.assertEqual(response.status_code == 200, expected)

    def test_get_score_file_not_exist(self):
        with self.assertRaises(FileNotFoundError):
            self.tester.get_score("/non/exist/file.mp4")

    @patch('os.walk')
    @patch('builtins.open', new_callable=mock_open)
    @patch.object(QualityGrade, 'get_score')
    def test_main_workflow(self, mock_score, mock_file, mock_walk):
        mock_walk.return_value = [
            (self.test_dir, [], ["test1.mp4", "invalid.txt"]),
        ]
        mock_score.return_value = Mock(content=b'{"score":90}')

        self._run_main()
        mock_file().write.assert_any_call("b'{\"score\":90}'\n")

    @patch('os.walk')
    @patch.object(QualityGrade, 'get_score')
    def test_non_mp4_filter(self, mock_score, mock_walk):
        mock_walk.return_value = [
            (self.test_dir, [], ["video.mp4", "invalid.txt"]),
        ]

        self._run_main()
        mock_score.assert_called_once_with('/fake/path/video.mp4')

    def _run_main(self, video_path=None):
        # 参数化路径输入
        base_dir = "/safe/base/dir"  # 替换为实际安全路径
        video_path = os.path.abspath(os.path.expanduser(video_path or "/fake/path"))
        
        # 安全路径验证
        if not os.path.realpath(video_path).startswith(base_dir):
            raise ValueError("Attempted to access restricted path")

        # 使用更高效的glob模式匹配（需要处理嵌套目录时保持os.walk）
        with patch('builtins.open', mock_open()) as mocked_file:
            try:
                for root, dirs, files in os.walk(video_path):
                    # 可选：忽略隐藏目录
                    dirs[:] = [d for d in dirs if not d.startswith('.')]
                    
                    for fl in files:
                        if fl.endswith('.mp4'):
                            file_path = os.path.join(root, fl)
                            
                            try:
                                # 获取评分
                                score_result = self.tester.get_score(file_path)
                                
                                # 验证结果有效性
                                if not hasattr(score_result, 'content'):
                                    raise ValueError("Invalid score format")
                                
                                # 写入结果
                                mocked_file().write(
                                    f"{file_path}: {score_result.content}\n"  # 增加文件名输出
                                )
                                
                            except Exception as e:
                                print(f"Error processing {file_path}: {str(e)}")
                                continue
                                
                # 验证文件写入操作
                mocked_file().write.assert_called()
                
            except Exception as e:
                raise

if __name__ == '__main__':
    unittest.main()
