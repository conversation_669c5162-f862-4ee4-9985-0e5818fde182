import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 创建图形
fig, ax = plt.subplots(1, 1, figsize=(20, 16))
ax.set_xlim(0, 20)
ax.set_ylim(0, 16)
ax.set_aspect('equal')

# 定义颜色方案
colors = {
    'game': '#FF6B6B',      # 游戏模块 - 红色
    'analysis': '#4ECDC4',  # 数据分析 - 青色
    'video': '#45B7D1',     # 视频处理 - 蓝色
    'frontend': '#96CEB4',  # 前端界面 - 绿色
    'results': '#FECA57',   # 分析结果 - 黄色
    'tools': '#DDA0DD',     # 工具配置 - 紫色
    'core': '#FF9FF3',      # 核心功能 - 粉色
}

# 绘制标题
ax.text(10, 15.5, '项目全景架构图', fontsize=24, fontweight='bold', 
        ha='center', va='center', color='#2C3E50')

# 绘制核心功能模块（中心位置）
core_box = FancyBboxPatch((8.5, 12), 3, 1.5, 
                          boxstyle="round,pad=0.1", 
                          facecolor=colors['core'], 
                          edgecolor='black', linewidth=2)
ax.add_patch(core_box)
ax.text(10, 12.75, '核心功能模块', fontsize=14, fontweight='bold', ha='center', va='center')

# 绘制游戏模块
game_modules = [
    ('打地鼠游戏', 2, 13, colors['game']),
    ('五子棋', 2, 11.5, colors['game']),
    ('扫雷', 2, 10, colors['game']),
    ('俄罗斯方块', 2, 8.5, colors['game']),
    ('播客原型', 2, 7, colors['game'])
]

for name, x, y, color in game_modules:
    box = FancyBboxPatch((x, y), 3, 1.2, boxstyle="round,pad=0.1", 
                        facecolor=color, edgecolor='black', alpha=0.8)
    ax.add_patch(box)
    ax.text(x+1.5, y+0.6, name, fontsize=11, ha='center', va='center', fontweight='bold')

# 绘制数据分析模块
analysis_modules = [
    ('eplus数据分析', 15, 13, colors['analysis']),
    ('视频质量评估', 15, 11.5, colors['analysis']),
    ('在线QoE评估', 15, 10, colors['analysis']),
    ('用户行为分析', 15, 8.5, colors['analysis'])
]

for name, x, y, color in analysis_modules:
    box = FancyBboxPatch((x, y), 3, 1.2, boxstyle="round,pad=0.1", 
                        facecolor=color, edgecolor='black', alpha=0.8)
    ax.add_patch(box)
    ax.text(x+1.5, y+0.6, name, fontsize=11, ha='center', va='center', fontweight='bold')

# 绘制视频处理模块
video_box = FancyBboxPatch((5, 5), 4, 1.2, boxstyle="round,pad=0.1", 
                          facecolor=colors['video'], edgecolor='black', alpha=0.8)
ax.add_patch(video_box)
ax.text(7, 5.6, '视频处理与下载', fontsize=12, ha='center', va='center', fontweight='bold')

# 绘制前端界面模块
frontend_box = FancyBboxPatch((11, 5), 4, 1.2, boxstyle="round,pad=0.1", 
                             facecolor=colors['frontend'], edgecolor='black', alpha=0.8)
ax.add_patch(frontend_box)
ax.text(13, 5.6, '前端界面设计', fontsize=12, ha='center', va='center', fontweight='bold')

# 绘制分析结果模块
results_box = FancyBboxPatch((3, 2.5), 4, 1.2, boxstyle="round,pad=0.1", 
                            facecolor=colors['results'], edgecolor='black', alpha=0.8)
ax.add_patch(results_box)
ax.text(5, 3.1, '分析结果存储', fontsize=12, ha='center', va='center', fontweight='bold')

# 绘制工具配置模块
tools_box = FancyBboxPatch((13, 2.5), 4, 1.2, boxstyle="round,pad=0.1", 
                          facecolor=colors['tools'], edgecolor='black', alpha=0.8)
ax.add_patch(tools_box)
ax.text(15, 3.1, '工具与配置', fontsize=12, ha='center', va='center', fontweight='bold')

# 绘制技术栈云图
tech_stack = [
    ('HTML5/CSS3', 1, 1, '#E34C26'),
    ('JavaScript', 3, 0.5, '#F7DF1E'),
    ('Python', 5, 1, '#3776AB'),
    ('Pandas', 7, 0.5, '#130654'),
    ('Matplotlib', 9, 1, '#11557C'),
    ('Tailwind', 11, 0.5, '#06B6D4'),
    ('Canvas API', 13, 1, '#239120'),
    ('FFmpeg', 15, 0.5, '#007808'),
    ('OpenCV', 17, 1, '#5C3EE8'),
    ('QoE算法', 19, 0.5, '#FF6B6B')
]

for tech, x, y, color in tech_stack:
    circle = plt.Circle((x, y), 0.4, color=color, alpha=0.7)
    ax.add_patch(circle)
    ax.text(x, y, tech, fontsize=9, ha='center', va='center', color='white', fontweight='bold')

# 绘制连接线 - 核心到各个模块
connections = [
    ((10, 12), (3.5, 13.6)),   # 核心到打地鼠
    ((10, 12), (3.5, 11.1)),   # 核心到五子棋
    ((10, 12), (3.5, 9.6)),    # 核心到扫雷
    ((10, 12), (3.5, 8.1)),    # 核心到俄罗斯方块
    ((10, 12), (3.5, 6.6)),    # 核心到播客原型
    ((10, 12), (16.5, 13.6)),  # 核心到eplus分析
    ((10, 12), (16.5, 12.1)),  # 核心到视频评估
    ((10, 12), (16.5, 10.6)),  # 核心到在线QoE
    ((10, 12), (16.5, 9.1)),   # 核心到用户行为
    ((10, 12), (7, 6.2)),      # 核心到视频处理
    ((10, 12), (13, 6.2)),     # 核心到前端界面
    ((10, 12), (5, 3.7)),      # 核心到分析结果
    ((10, 12), (15, 3.7)),     # 核心到工具配置
]

for start, end in connections:
    ax.annotate('', xy=end, xytext=start,
                arrowprops=dict(arrowstyle='->', color='gray', lw=1, alpha=0.6))

# 添加模块说明文字
ax.text(1, 14.5, '🎮 游戏娱乐', fontsize=16, fontweight='bold', color=colors['game'])
ax.text(14, 14.5, '📊 数据分析', fontsize=16, fontweight='bold', color=colors['analysis'])
ax.text(1, 0.5, '💻 技术栈', fontsize=14, fontweight='bold', color='#2C3E50')

# 添加使用场景说明
usage_scenarios = [
    "教育领域：编程教学游戏化",
    "娱乐应用：休闲游戏平台",
    "企业应用：质量监控平台",
    "数据分析：用户行为洞察"
]

for i, scenario in enumerate(usage_scenarios):
    ax.text(0.5, 4.5-i*0.4, f"• {scenario}", fontsize=10, color='#34495E')

# 设置坐标轴
ax.set_xticks([])
ax.set_yticks([])
ax.axis('off')

# 保存图片
plt.tight_layout()
plt.savefig('项目架构图.png', dpi=300, bbox_inches='tight', facecolor='white')
plt.show()

# 创建技术栈饼图
fig2, ax2 = plt.subplots(1, 2, figsize=(16, 8))

# 技术栈分布饼图
tech_data = {
    '前端技术': 35,
    'Python后端': 25,
    '数据分析': 20,
    '机器学习': 15,
    '工具配置': 5
}

ax2[0].pie(tech_data.values(), labels=tech_data.keys(), autopct='%1.1f%%', 
           colors=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57'])
ax2[0].set_title('技术栈分布', fontsize=16, fontweight='bold')

# 功能模块分布饼图
module_data = {
    '游戏娱乐': 30,
    '数据分析': 25,
    '视频处理': 20,
    '前端界面': 15,
    '工具配置': 10
}

ax2[1].pie(module_data.values(), labels=module_data.keys(), autopct='%1.1f%%',
           colors=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57'])
ax2[1].set_title('功能模块分布', fontsize=16, fontweight='bold')

plt.tight_layout()
plt.savefig('技术栈分析.png', dpi=300, bbox_inches='tight', facecolor='white')
plt.show()

print("项目架构图已生成：项目架构图.png")
print("技术栈分析图已生成：技术栈分析.png")