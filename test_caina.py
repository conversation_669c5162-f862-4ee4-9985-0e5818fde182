#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import List

class Solution:
    def numDistinct(self, s: str, t: str) -> int:
        m, n = len(s), len(t)
        
        if m < n:
            return 0
        
        dp = [0] * (n + 1)
        dp[n] = 1
        
        for i in range(m - 1, -1, -1):
            prev = dp[n]
            for j in range(n - 1, -1, -1):
                current = dp[j]
                if s[i] == t[j]:
                    dp[j] = dp[j + 1] + prev
                else:
                    dp[j] = prev
                prev = current
        
        return dp[0]

    def numDistinct_optimized_correct(self, s: str, t: str) -> int:
        m, n = len(s), len(t)
        
        if m < n:
            return 0
        
        dp = [0] * (n + 1)
        dp[n] = 1
        
        for i in range(m - 1, -1, -1):
            prev = dp[n]
            for j in range(n - 1, -1, -1):
                temp = dp[j]
                if s[i] == t[j]:
                    dp[j] = dp[j + 1] + prev
                else:
                    dp[j] = prev
                prev = temp
        
        return dp[0]

    def numDistinct_original(self, s: str, t: str) -> int:
        m, n = len(s), len(t)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        for i in range(m + 1):
            dp[i][n] = 1
        for i in range(m - 1, -1 , -1):
            for j in range(n - 1, -1, -1):
                if s[i] == t[j]:
                    dp[i][j] = dp[i + 1][j + 1] + dp[i + 1][j]
                else:
                    dp[i][j] = dp[i + 1][j]
        return dp[0][0]

    def numDistinct_forward_correct(self, s: str, t: str) -> int:
        m, n = len(s), len(t)
        if m < n:
            return 0
        
        dp = [0] * (n + 1)
        dp[0] = 1
        
        for i in range(1, m + 1):
            for j in range(min(i, n), 0, -1):
                if s[i - 1] == t[j - 1]:
                    dp[j] = dp[j] + dp[j - 1]
        
        return dp[n]

    def numDistinct_fixed(self, s: str, t: str) -> int:
        m, n = len(s), len(t)
        
        if m < n:
            return 0
        
        dp = [0] * (n + 1)
        dp[n] = 1
        
        for i in range(m - 1, -1, -1):
            prev = dp[n]
            for j in range(n - 1, -1, -1):
                current = dp[j]
                if s[i] == t[j]:
                    dp[j] = dp[j + 1] + prev
                else:
                    dp[j] = prev
                prev = current
        
        return dp[0]

if __name__ == "__main__":
    s = Solution()
    
    print("=== 测试优化版本 ===")
    print("原始版本:")
    print(s.numDistinct_original("rabbbit", "rabbit"))
    print(s.numDistinct_original("babgbag", "bag"))
    
    print("修复版本:")
    print(s.numDistinct_fixed("rabbbit", "rabbit"))
    print(s.numDistinct_fixed("babgbag", "bag"))
    
    print("前向版本:")
    print(s.numDistinct_forward_correct("rabbbit", "rabbit"))
    print(s.numDistinct_forward_correct("babgbag", "bag"))
    
    print("\n验证一致性:")
    test_cases = [
        ("rabbbit", "rabbit"),
        ("babgbag", "bag"),
        ("a", "a"),
        ("a", "b"),
        ("", ""),
        ("abc", ""),
        ("", "a")
    ]
    
    for s_str, t_str in test_cases:
        original = s.numDistinct_original(s_str, t_str)
        fixed = s.numDistinct_fixed(s_str, t_str)
        forward = s.numDistinct_forward_correct(s_str, t_str)
        print(f"s='{s_str}', t='{t_str}': 原始={original}, 修复={fixed}, 前向={forward}, {'✓' if original == fixed == forward else '✗'}")

    print("\n性能测试:")
    import time
    s_large = "a" * 1000
    t_large = "a" * 500
    
    start = time.time()
    result1 = s.numDistinct_original(s_large, t_large)
    time1 = time.time() - start
    
    start = time.time()
    result2 = s.numDistinct_fixed(s_large, t_large)
    time2 = time.time() - start
    
    start = time.time()
    result3 = s.numDistinct_forward_correct(s_large, t_large)
    time3 = time.time() - start
    
    print(f"原始版本: 结果={result1}, 时间={time1:.6f}秒")
    print(f"修复版本: 结果={result2}, 时间={time2:.6f}秒")
    print(f"前向版本: 结果={result3}, 时间={time3:.6f}秒")
    print(f"修复版本性能提升: {time1/time2:.2f}x")
    print(f"前向版本性能提升: {time1/time3:.2f}x")
    print(f"结果一致性: {'✓' if result1 == result2 == result3 else '✗'}")

