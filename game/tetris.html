<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #1a1c2c 0%, #2a3042 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            padding: 20px;
        }
        
        .game-container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            display: flex;
            gap: 30px;
        }
        
        .game-board {
            display: grid;
            grid-template-columns: repeat(10, 30px);
            grid-template-rows: repeat(20, 30px);
            gap: 1px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 10px;
            position: relative;
        }
        
        .tetris-cell {
            width: 30px;
            height: 30px;
            border-radius: 4px;
            transition: all 0.1s ease;
        }
        
        .next-piece-board {
            display: grid;
            grid-template-columns: repeat(4, 25px);
            grid-template-rows: repeat(4, 25px);
            gap: 1px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 8px;
        }
        
        .next-cell {
            width: 25px;
            height: 25px;
            border-radius: 3px;
        }
        
        /* 方块颜色 */
        .color-0 { background: rgba(0, 0, 0, 0.2); }
        .color-1 { background: linear-gradient(135deg, #00f0f0, #00d0d0); } /* I */
        .color-2 { background: linear-gradient(135deg, #0000f0, #0000d0); } /* J */
        .color-3 { background: linear-gradient(135deg, #f0a000, #d08000); } /* L */
        .color-4 { background: linear-gradient(135deg, #f0f000, #d0d000); } /* O */
        .color-5 { background: linear-gradient(135deg, #00f000, #00d000); } /* S */
        .color-6 { background: linear-gradient(135deg, #a000f0, #8000d0); } /* T */
        .color-7 { background: linear-gradient(135deg, #f00000, #d00000); } /* Z */
        
        .flash-animation {
            animation: flash 0.3s ease-in-out;
        }
        
        @keyframes flash {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; background: white; }
        }
        
        .score-popup {
            position: absolute;
            color: white;
            font-weight: bold;
            font-size: 1.2em;
            pointer-events: none;
            animation: float-up 1s ease-out forwards;
        }
        
        @keyframes float-up {
            0% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(-50px); opacity: 0; }
        }
        
        .game-info {
            color: white;
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .info-title {
            font-size: 0.9em;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 5px;
        }
        
        .info-value {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .control-button {
            width: 100%;
            padding: 12px;
            border-radius: 8px;
            border: none;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .control-button:hover {
            transform: translateY(-2px);
        }
        
        .start-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }
        
        .pause-button {
            background: linear-gradient(135deg, #FFC107, #FFB300);
        }
        
        .overlay {
            position: absolute;
            inset: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
        }
        
        .overlay-text {
            color: white;
            font-size: 2em;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }
        
        .restart-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border: none;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .restart-button:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="index.html" 
       class="fixed top-4 left-4 px-4 py-2 rounded-xl 
              bg-white/10 backdrop-blur text-white 
              hover:bg-white/20 transition-all duration-300
              flex items-center gap-2 group">
        <span class="transform transition-transform group-hover:-translate-x-1">←</span>
        返回首页
    </a>

    <div class="game-container">
        <div class="game-board-container">
            <div class="game-board" id="game-board">
                <div class="overlay hidden" id="game-over">
                    <div class="overlay-text">游戏结束</div>
                    <button class="restart-button" id="restart-button">重新开始</button>
                </div>
                <div class="overlay hidden" id="pause-overlay">
                    <div class="overlay-text">已暂停</div>
                </div>
            </div>
        </div>
        
        <div class="game-info">
            <div class="info-box">
                <h3 class="info-title">下一个方块</h3>
                <div class="next-piece-board" id="next-piece-board"></div>
            </div>
            
            <div class="info-box">
                <h3 class="info-title">分数</h3>
                <div class="info-value" id="score">0</div>
            </div>
            
            <div class="info-box">
                <h3 class="info-title">等级</h3>
                <div class="info-value" id="level">1</div>
            </div>
            
            <div class="info-box">
                <h3 class="info-title">已消除行数</h3>
                <div class="info-value" id="lines">0</div>
            </div>
            
            <div class="controls">
                <button id="start-button" class="control-button start-button mb-3">
                    开始游戏
                </button>
                <button id="pause-button" class="control-button pause-button">
                    暂停
                </button>
            </div>
            
            <div class="info-box mt-6">
                <h3 class="info-title mb-3">操作说明</h3>
                <div class="space-y-2 text-sm text-white/70">
                    <div>← → : 左右移动</div>
                    <div>↑ : 旋转方块</div>
                    <div>↓ : 加速下落</div>
                    <div>空格 : 直接落底</div>
                    <div>P : 暂停/继续</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // JavaScript代码将在下一部分提供
        class 俄罗斯方块 {
    constructor() {
        // 游戏配置
        this.行数 = 20;
        this.列数 = 10;
        this.空白 = 0;
        this.预览大小 = 4;
        
        // 方块形状定义
        this.方块形状 = {
            1: [[1, 1, 1, 1]],                // I
            2: [[1, 0, 0], [1, 1, 1]],        // J
            3: [[0, 0, 1], [1, 1, 1]],        // L
            4: [[1, 1], [1, 1]],              // O
            5: [[0, 1, 1], [1, 1, 0]],        // S
            6: [[0, 1, 0], [1, 1, 1]],        // T
            7: [[1, 1, 0], [0, 1, 1]]         // Z
        };
        
        // 游戏元素
        this.游戏面板 = document.getElementById('game-board');
        this.预览面板 = document.getElementById('next-piece-board');
        this.分数显示 = document.getElementById('score');
        this.等级显示 = document.getElementById('level');
        this.行数显示 = document.getElementById('lines');
        this.开始按钮 = document.getElementById('start-button');
        this.暂停按钮 = document.getElementById('pause-button');
        this.结束提示 = document.getElementById('game-over');
        this.暂停提示 = document.getElementById('pause-overlay');
        
        // 游戏状态
        this.游戏板 = [];
        this.分数 = 0;
        this.等级 = 1;
        this.消除行数 = 0;
        this.游戏结束 = false;
        this.已暂停 = false;
        this.正在游戏 = false;
        this.游戏循环 = null;
        this.当前方块 = null;
        this.下一个方块 = null;
        
        // 初始化游戏
        this.初始化游戏板();
        this.初始化预览板();
        this.绑定事件();
    }
    
    初始化游戏板() {
        this.游戏面板.innerHTML = '';
        for (let i = 0; i < this.行数; i++) {
            for (let j = 0; j < this.列数; j++) {
                const 格子 = document.createElement('div');
                格子.className = 'tetris-cell color-0';
                this.游戏面板.appendChild(格子);
            }
        }
        
        this.游戏面板.appendChild(this.结束提示);
        this.游戏面板.appendChild(this.暂停提示);
    }
    
    初始化预览板() {
        this.预览面板.innerHTML = '';
        for (let i = 0; i < this.预览大小 * this.预览大小; i++) {
            const 格子 = document.createElement('div');
            格子.className = 'next-cell color-0';
            this.预览面板.appendChild(格子);
        }
    }
    
    绑定事件() {
        this.开始按钮.addEventListener('click', () => this.开始游戏());
        this.暂停按钮.addEventListener('click', () => this.切换暂停());
        
        document.addEventListener('keydown', (e) => {
            if (!this.正在游戏 || this.游戏结束) return;
            if (this.已暂停 && e.key !== 'p' && e.key !== 'P') return;
            
            switch (e.key) {
                case 'ArrowLeft':
                    this.移动方块(-1, 0);
                    break;
                case 'ArrowRight':
                    this.移动方块(1, 0);
                    break;
                case 'ArrowDown':
                    this.移动方块(0, 1);
                    break;
                case 'ArrowUp':
                    this.旋转方块();
                    break;
                case ' ':
                    this.快速下落();
                    break;
                case 'p':
                case 'P':
                    this.切换暂停();
                    break;
            }
        });
    }
    
    开始游戏() {
        // 重置游戏状态
        this.游戏板 = Array(this.行数).fill().map(() => Array(this.列数).fill(this.空白));
        this.分数 = 0;
        this.等级 = 1;
        this.消除行数 = 0;
        this.游戏结束 = false;
        this.已暂停 = false;
        this.正在游戏 = true;
        
        // 更新界面
        this.分数显示.textContent = this.分数;
        this.等级显示.textContent = this.等级;
        this.行数显示.textContent = this.消除行数;
        this.结束提示.classList.add('hidden');
        this.暂停提示.classList.add('hidden');
        
        // 清空游戏板
        this.渲染游戏板();
        
        // 生成第一个方块
        this.下一个方块 = this.生成随机方块();
        this.生成新方块();
        
        // 开始游戏循环
        if (this.游戏循环) clearInterval(this.游戏循环);
        this.游戏循环 = setInterval(() => this.游戏主循环(), this.获取游戏速度());
    }
    
    游戏主循环() {
        if (this.已暂停 || this.游戏结束) return;
        
        if (!this.移动方块(0, 1)) {
            this.放置方块();
            const 消除数 = this.检查消除();
            this.更新分数(消除数);
            
            if (!this.生成新方块()) {
                this.结束游戏();
            }
        }
    }

    生成随机方块() {
        const 索引 = Math.floor(Math.random() * 7) + 1;
        const 形状 = this.方块形状[索引];
        
        return {
            形状,
            颜色: 索引,
            行: 0,
            列: Math.floor((this.列数 - 形状[0].length) / 2)
        };
    }
    
    生成新方块() {
        this.当前方块 = this.下一个方块;
        this.下一个方块 = this.生成随机方块();
        
        this.渲染预览方块();
        
        if (!this.检查位置有效(this.当前方块)) {
            return false;
        }
        
        this.渲染方块();
        return true;
    }
    
    渲染预览方块() {
        const 预览格子 = this.预览面板.querySelectorAll('.next-cell');
        预览格子.forEach(格子 => 格子.className = 'next-cell color-0');
        
        const { 形状, 颜色 } = this.下一个方块;
        const 偏移行 = Math.floor((this.预览大小 - 形状.length) / 2);
        const 偏移列 = Math.floor((this.预览大小 - 形状[0].length) / 2);
        
        for (let i = 0; i < 形状.length; i++) {
            for (let j = 0; j < 形状[i].length; j++) {
                if (形状[i][j]) {
                    const 行 = i + 偏移行;
                    const 列 = j + 偏移列;
                    const 索引 = 行 * this.预览大小 + 列;
                    预览格子[索引].className = `next-cell color-${颜色}`;
                }
            }
        }
    }
    
    检查位置有效(方块) {
        const { 形状, 行, 列 } = 方块;
        
        for (let i = 0; i < 形状.length; i++) {
            for (let j = 0; j < 形状[i].length; j++) {
                if (形状[i][j]) {
                    const 新行 = 行 + i;
                    const 新列 = 列 + j;
                    
                    if (新行 < 0 || 新行 >= this.行数 || 
                        新列 < 0 || 新列 >= this.列数 || 
                        this.游戏板[新行][新列] !== this.空白) {
                        return false;
                    }
                }
            }
        }
        
        return true;
    }
    
    移动方块(列偏移, 行偏移) {
        const 新方块 = {
            ...this.当前方块,
            行: this.当前方块.行 + 行偏移,
            列: this.当前方块.列 + 列偏移
        };
        
        if (this.检查位置有效(新方块)) {
            this.清除方块();
            this.当前方块 = 新方块;
            this.渲染方块();
            return true;
        }
        
        return false;
    }
    
    旋转方块() {
        const 旧形状 = this.当前方块.形状;
        const 新形状 = [];
        
        for (let 列 = 0; 列 < 旧形状[0].length; 列++) {
            新形状.push([]);
            for (let 行 = 旧形状.length - 1; 行 >= 0; 行--) {
                新形状[列].push(旧形状[行][列]);
            }
        }
        
        const 新方块 = {
            ...this.当前方块,
            形状: 新形状
        };
        
        if (this.检查位置有效(新方块)) {
            this.清除方块();
            this.当前方块 = 新方块;
            this.渲染方块();
            return true;
        }
        
        // 尝试墙踢
        const 踢墙测试 = [
            { 列: 1, 行: 0 },
            { 列: -1, 行: 0 },
            { 列: 0, 行: -1 },
            { 列: 2, 行: 0 },
            { 列: -2, 行: 0 }
        ];
        
        for (const 测试 of 踢墙测试) {
            const 测试方块 = {
                ...新方块,
                行: 新方块.行 + 测试.行,
                列: 新方块.列 + 测试.列
            };
            
            if (this.检查位置有效(测试方块)) {
                this.清除方块();
                this.当前方块 = 测试方块;
                this.渲染方块();
                return true;
            }
        }
        
        return false;
    }
    
    快速下落() {
        let 下落距离 = 0;
        
        while (this.移动方块(0, 1)) {
            下落距离++;
        }
        
        this.分数 += 下落距离;
        this.分数显示.textContent = this.分数;
        
        // 确保方块正确放置
        if (this.当前方块) {
            this.放置方块();
            const 消除数 = this.检查消除();
            this.更新分数(消除数);
        
            if (!this.生成新方块()) {
                this.结束游戏();
            }
        }
    }
    
    放置方块() {
        const { 形状, 行, 列, 颜色 } = this.当前方块;
        
        for (let i = 0; i < 形状.length; i++) {
            for (let j = 0; j < 形状[i].length; j++) {
                if (形状[i][j]) {
                    const 游戏板行 = 行 + i;
                    const 游戏板列 = 列 + j;
                    if (游戏板行 >= 0 && 游戏板行 < this.行数 && 
                        游戏板列 >= 0 && 游戏板列 < this.列数) {
                        this.游戏板[游戏板行][游戏板列] = 颜色;
                    }
                }
            }
        }
        
        this.渲染游戏板();
    }
    
    检查消除() {
        let 消除数 = 0;
        const 待消除行 = [];
        
        for (let 行 = this.行数 - 1; 行 >= 0; 行--) {
            if (this.游戏板[行].every(格子 => 格子 !== this.空白)) {
                待消除行.push(行);
                消除数++;
            }
        }
        
        if (消除数 > 0) {
            this.闪烁消除行(待消除行, () => {
                for (const 行 of 待消除行) {
                    this.游戏板.splice(行, 1);
                    this.游戏板.unshift(Array(this.列数).fill(this.空白));
                }
                this.渲染游戏板();
            });
        }
        
        return 消除数;
    }
    
    闪烁消除行(待消除行, 回调) {
        const 格子们 = this.游戏面板.querySelectorAll('.tetris-cell');
        
        待消除行.forEach(行 => {
            for (let 列 = 0; 列 < this.列数; 列++) {
                const 索引 = 行 * this.列数 + 列;
                格子们[索引].classList.add('flash-animation');
            }
        });
        
        setTimeout(() => {
            待消除行.forEach(行 => {
                for (let 列 = 0; 列 < this.列数; 列++) {
                    const 索引 = 行 * this.列数 + 列;
                    格子们[索引].classList.remove('flash-animation');
                }
            });
            
            回调();
        }, 300);
    }
    
    更新分数(消除数) {
        if (消除数 === 0) return;
        
        const 行分数 = [0, 100, 300, 500, 800];
        this.分数 += 行分数[消除数] * this.等级;
        this.消除行数 += 消除数;
        
        this.等级 = Math.floor(this.消除行数 / 10) + 1;
        
        this.分数显示.textContent = this.分数;
        this.等级显示.textContent = this.等级;
        this.行数显示.textContent = this.消除行数;
        
        clearInterval(this.游戏循环);
        this.游戏循环 = setInterval(() => this.游戏主循环(), this.获取游戏速度());
        
        // 显示得分动画
        this.显示得分特效(行分数[消除数] * this.等级);
    }
    
    显示得分特效(分数) {
        const 特效 = document.createElement('div');
        特效.className = 'score-popup';
        特效.textContent = `+${分数}`;
        特效.style.left = `${this.游戏面板.offsetWidth / 2}px`;
        特效.style.top = `${this.游戏面板.offsetHeight / 2}px`;
        this.游戏面板.appendChild(特效);
        
        setTimeout(() => 特效.remove(), 1000);
    }
    
    获取游戏速度() {
        return Math.max(100, 1000 - (this.等级 - 1) * 100);
    }
    
    渲染方块() {
        const { 形状, 行, 列, 颜色 } = this.当前方块;
        const 格子们 = this.游戏面板.querySelectorAll('.tetris-cell');
        
        for (let i = 0; i < 形状.length; i++) {
            for (let j = 0; j < 形状[i].length; j++) {
                if (形状[i][j]) {
                    const 游戏板行 = 行 + i;
                    const 游戏板列 = 列 + j;
                    if (游戏板行 >= 0 && 游戏板行 < this.行数 && 
                        游戏板列 >= 0 && 游戏板列 < this.列数) {
                        const 索引 = 游戏板行 * this.列数 + 游戏板列;
                        格子们[索引].className = `tetris-cell color-${颜色}`;
                    }
                }
            }
        }
    }
    
    清除方块() {
        const { 形状, 行, 列 } = this.当前方块;
        const 格子们 = this.游戏面板.querySelectorAll('.tetris-cell');
        
        for (let i = 0; i < 形状.length; i++) {
            for (let j = 0; j < 形状[i].length; j++) {
                if (形状[i][j]) {
                    const 游戏板行 = 行 + i;
                    const 游戏板列 = 列 + j;
                    if (游戏板行 >= 0 && 游戏板行 < this.行数 && 
                        游戏板列 >= 0 && 游戏板列 < this.列数) {
                        const 索引 = 游戏板行 * this.列数 + 游戏板列;
                        格子们[索引].className = 'tetris-cell color-0';
                    }
                }
            }
        }
    }
    
    渲染游戏板() {
        const 格子们 = this.游戏面板.querySelectorAll('.tetris-cell');
        
        for (let i = 0; i < this.行数; i++) {
            for (let j = 0; j < this.列数; j++) {
                const 索引 = i * this.列数 + j;
                const 值 = this.游戏板[i][j];
                格子们[索引].className = `tetris-cell color-${值}`;
            }
        }
    }
    
    切换暂停() {
        if (!this.正在游戏 || this.游戏结束) return;
        
        this.已暂停 = !this.已暂停;
        
        if (this.已暂停) {
            this.暂停提示.classList.remove('hidden');
            this.暂停按钮.textContent = '继续';
        } else {
            this.暂停提示.classList.add('hidden');
            this.暂停按钮.textContent = '暂停';
        }
    }
    
    结束游戏() {
        this.游戏结束 = true;
        this.正在游戏 = false;
        clearInterval(this.游戏循环);
        this.结束提示.classList.remove('hidden');
        
        // 保存最高分
        this.保存最高分();
    }
    
    保存最高分() {
        const 当前最高分 = localStorage.getItem('tetris_最高分') || 0;
        if (this.分数 > 当前最高分) {
            localStorage.setItem('tetris_最高分', this.分数);
            this.显示新纪录提示();
        }
    }
    
    显示新纪录提示() {
        const 提示 = document.createElement('div');
        提示.className = 'score-popup';
        提示.textContent = '新纪录！';
        提示.style.color = '#FFD700';
        提示.style.fontSize = '2em';
        提示.style.left = `${this.游戏面板.offsetWidth / 2}px`;
        提示.style.top = `${this.游戏面板.offsetHeight / 2 - 50}px`;
        this.游戏面板.appendChild(提示);
        
        setTimeout(() => 提示.remove(), 2000);
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const 游戏 = new 俄罗斯方块();
    
    // 添加触摸控制支持
    let 触摸起始X = null;
    let 触摸起始Y = null;
    
    document.addEventListener('touchstart', (e) => {
        触摸起始X = e.touches[0].clientX;
        触摸起始Y = e.touches[0].clientY;
    });
    
    document.addEventListener('touchmove', (e) => {
        if (!触摸起始X || !触摸起始Y) return;
        
        const 触摸结束X = e.touches[0].clientX;
        const 触摸结束Y = e.touches[0].clientY;
        
        const 差异X = 触摸结束X - 触摸起始X;
        const 差异Y = 触摸结束Y - 触摸起始Y;
        
        // 检测滑动方向
        if (Math.abs(差异X) > Math.abs(差异Y)) {
            // 水平滑动
            if (差异X > 30) {
                游戏.移动方块(1, 0);
            } else if (差异X < -30) {
                游戏.移动方块(-1, 0);
            }
        } else {
            // 垂直滑动
            if (差异Y > 30) {
                游戏.移动方块(0, 1);
            } else if (差异Y < -30) {
                游戏.旋转方块();
            }
        }
        
        触摸起始X = 触摸结束X;
        触摸起始Y = 触摸结束Y;
    });
    
    document.addEventListener('touchend', () => {
        触摸起始X = null;
        触摸起始Y = null;
    });
    
    // 添加双击快速下落
    let 最后点击时间 = 0;
    document.addEventListener('touchstart', (e) => {
        const 当前时间 = new Date().getTime();
        const 时间差 = 当前时间 - 最后点击时间;
        
        if (时间差 < 300) {  // 双击检测阈值
            游戏.快速下落();
            e.preventDefault();  // 防止双击缩放
        }
        
        最后点击时间 = 当前时间;
    });
});

// 添加音效支持
const 音效 = {
    移动: new Audio('sounds/move.mp3'),
    旋转: new Audio('sounds/rotate.mp3'),
    放置: new Audio('sounds/drop.mp3'),
    消除: new Audio('sounds/clear.mp3'),
    游戏结束: new Audio('sounds/gameover.mp3')
};

// 预加载音效
Object.values(音效).forEach(音频 => {
    音频.load();
    // 设置音量
    音频.volume = 0.3;
});

// 播放音效函数
function 播放音效(名称) {
    if (音效[名称]) {
        音效[名称].currentTime = 0;
        音效[名称].play().catch(() => {});
    }
}
    </script>
</body>
</html>