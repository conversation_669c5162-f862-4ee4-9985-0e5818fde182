#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import List
from collections import Counter


# 给定一个字符串 s 和一个字符串数组 words。 words 中所有字符串 长度相同。

#  s 中的 串联子串 是指一个包含  words 中所有字符串以任意顺序排列连接起来的子串。

# 例如，如果 words = ["ab","cd","ef"]， 那么 "abcdef"， "abefcd"，"cdabef"， "cdefab"，"efabcd"， 和 "efcdab" 都是串联子串。 "acdbef" 不是串联子串，因为他不是任何 words 排列的连接。
# 返回所有串联子串在 s 中的开始索引。你可以以 任意顺序 返回答案。
class Solution:
    def findSubstring(self, s: str, words: List[str]) -> List[int]:
        if not s or not words:
            return []

        word_len = len(words[0])
        word_count = len(words)
        substring_len = word_len * word_count
        if len(s) < substring_len:
            return []

        word_counter = Counter(words)
        result = []
        for i in range(len(s) - substring_len + 1):
            seen = Counter()
            for j in range(i, i + substring_len, word_len):
                word = s[j:j + word_len]
                if word in word_counter:
                    seen[word] += 1
                    if seen[word] > word_counter[word]:
                        break
                else:
                    break
            else:
                result.append(i)
        return result

if __name__ == "__main__":
    s = Solution()
    print(s.findSubstring("barfoothefoobarman", ["foo","bar"]))
    print(s.findSubstring("wordgoodgoodgoodbestword", ["word","good","best","word"]))
    print(s.findSubstring("barfoofoobarthefoobarman", ["bar","foo","the"]))
    print(s.findSubstring("lingmindraboofooowingdingbarrwingmonkeypoundcake", ["foo","b","bar","mong"]))
            




