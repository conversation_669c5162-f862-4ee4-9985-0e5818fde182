# coding:utf-8
import os
import json

import csv

if __name__ == '__main__':
    # with open("iOS20230901.csv","r") as file:
    #     reader = csv.reader(file)
    #     for row in reader:
    #
    #         print(row)
    #         break

    uid_djid = []
    with open("0905iosdata_ori.txt", "r") as f:
        for line in f.readlines():
            uid_duiju = {}
            uid_duiju["uid"] = line.split(',')[1]
            uid_duiju["duiju"] = line.split(',')[0]
            uid_duiju["url"] = line.split(',')[2].strip()
            uid_djid.append(uid_duiju)


    # #print(uid_djid)
    #
    #
    # data_all = []
    #
    # ori_data = []
    # with open("iOS20230901.csv","r") as file:
    #     reader = csv.reader(file)
    #     for row in reader:
    #         ori_data.append(row)
    #
    # for uid_duiju in uid_djid:
    #     uid = uid_duiju["uid"]
    #     duiju = uid_duiju["duiju"]
    #
    #     for row in ori_data:
    #         if row[1] == uid and row[11] == duiju:
    #             print(uid, duiju)
    #             data_one = []
    #             # print(row[1], row[3], row[6], row[9], row[11],row[15], row[16])
    #             data_one.append(row[1])
    #             data_one.append(row[3])
    #             data_one.append(row[6])
    #             data_one.append(row[9])
    #             data_one.append(row[11])
    #             data_one.append(row[15])
    #             data_one.append(row[16])
    #             data_one.append(row[18])
    #             data_one.append(uid_duiju["url"])
    #             data_all.append(data_one)
    #             break
    #
    #
    # with open('example.csv', 'w', newline='') as csvfile:
    #     writer = csv.writer(csvfile)
    #     for data in data_all:
    #         writer.writerow(data)




