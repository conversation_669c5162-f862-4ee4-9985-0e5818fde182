<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>五子棋 - 在线对战</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-image: radial-gradient(circle at center, #2C3E50 0%, #1A1A1A 100%);
        }

        .board-container {
            width: 600px;
            height: 600px;
            position: relative;
            margin: 10px 0;
        }

        .board {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #D4B08C 0%, #B68B4C 100%);
            position: relative;
            border-radius: 12px;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.4),
                inset 0 2px 4px rgba(255, 255, 255, 0.2);
            border: 8px solid #8B4513;
            box-sizing: border-box;
        }

        .grid-line {
            position: absolute;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1;
        }

        .vertical {
            width: 2px;
            height: calc(100% - 40px);
            top: 20px;
            transform: translateX(-50%);
        }

        .horizontal {
            height: 2px;
            width: calc(100% - 40px);
            left: 20px;
            transform: translateY(-50%);
        }

        .intersection {
            position: absolute;
            width: 30px;
            height: 30px;
            transform: translate(-50%, -50%);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }

        .intersection:hover::before {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        /* 黑棋提示样式 */
        .board.black-next:not(.game-over) .intersection:hover::before {
            background: rgba(0, 0, 0, 0.3);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
        }

        /* 白棋提示样式 */
        .board.white-next:not(.game-over) .intersection:hover::before {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        /* 已有棋子或游戏结束时不显示提示 */
        .intersection:has(.piece):hover::before,
        .board.game-over .intersection:hover::before {
            display: none;
        }

        .piece {
            width: 26px;
            height: 26px;
            border-radius: 50%;
            position: relative;
            transform: scale(0);
            animation: place-piece 0.3s ease forwards;
        }

        .piece.black {
            background: radial-gradient(circle at 35% 35%, #666 0%, #000 100%);
            box-shadow: 
                2px 2px 4px rgba(0, 0, 0, 0.4),
                inset -2px -2px 4px rgba(0, 0, 0, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.2);
        }

        .piece.white {
            background: radial-gradient(circle at 35% 35%, #fff 0%, #ddd 100%);
            box-shadow: 
                2px 2px 4px rgba(0, 0, 0, 0.2),
                inset -2px -2px 4px rgba(0, 0, 0, 0.1),
                inset 2px 2px 4px rgba(255, 255, 255, 0.8);
        }

        @keyframes place-piece {
            0% { transform: scale(0); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .star-point {
            position: absolute;
            width: 8px;
            height: 8px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
        }

        .winning-piece {
            animation: winning-animation 1s ease infinite;
        }

        @keyframes winning-animation {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); box-shadow: 0 0 20px rgba(255, 215, 0, 0.6); }
            100% { transform: scale(1); }
        }

        .game-container {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.05);
        }

        @media (max-width: 640px) {
            .board-container {
                width: 100%;
                height: auto;
                aspect-ratio: 1;
            }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <!-- 添加返回按钮 -->
    <a href="index.html" 
       class="fixed top-4 left-4 px-4 py-2 rounded-xl 
              bg-gray-800/50 backdrop-blur text-white 
              hover:bg-gray-700/50 transition-all duration-300
              flex items-center gap-2 group">
        <span class="transform transition-transform group-hover:-translate-x-1">←</span>
        返回首页
    </a>

    <div class="game-container rounded-3xl p-6 max-w-4xl w-full mx-auto">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-200 to-yellow-500 mb-4">
                五子棋
            </h1>
            
            <div class="flex flex-col items-center gap-4">
                <!-- 合并后的状态面板 -->
                <div class="flex items-center justify-between w-full max-w-[600px] bg-gray-800/50 rounded-2xl p-4">
                    <!-- 左侧黑棋信息 -->
                    <div class="text-center">
                        <div class="flex items-center gap-3 mb-2">
                            <div class="w-6 h-6 rounded-full bg-black shadow-lg"></div>
                            <span class="text-white font-medium" id="black-time">00:00</span>
                        </div>
                        <div class="text-yellow-400 font-bold" id="black-wins">胜: 0</div>
                        <div class="text-gray-400 text-sm mt-1" id="black-undo">悔棋: 3</div>
                    </div>

                    <!-- 中间状态和选择先手 -->
                    <div class="flex flex-col items-center gap-3">
                        <div class="text-gray-400 font-bold text-xl">VS</div>
                        <div id="status" class="text-yellow-400 font-bold text-lg min-w-[80px] text-center"></div>
                        <div class="flex gap-2 mt-1">
                            <button id="select-black" 
                                    class="flex items-center gap-1 px-2 py-1 rounded-lg transition-all duration-300
                                           bg-gradient-to-r from-gray-900 to-gray-800 
                                           hover:from-gray-800 hover:to-gray-700 active:scale-95
                                           text-sm">
                                <div class="w-3 h-3 rounded-full bg-black"></div>
                                <span class="text-white">先手</span>
                            </button>
                            <button id="select-white" 
                                    class="flex items-center gap-1 px-2 py-1 rounded-lg transition-all duration-300
                                           bg-gradient-to-r from-gray-200 to-gray-100
                                           hover:from-gray-100 hover:to-gray-50 active:scale-95
                                           text-sm">
                                <div class="w-3 h-3 rounded-full bg-white"></div>
                                <span class="text-gray-800">先手</span>
                            </button>
                        </div>
                    </div>

                    <!-- 右侧白棋信息 -->
                    <div class="text-center">
                        <div class="flex items-center gap-3 mb-2">
                            <div class="w-6 h-6 rounded-full bg-white shadow-lg"></div>
                            <span class="text-white font-medium" id="white-time">00:00</span>
                        </div>
                        <div class="text-yellow-400 font-bold" id="white-wins">胜: 0</div>
                        <div class="text-gray-400 text-sm mt-1" id="white-undo">悔棋: 3</div>
                    </div>
                </div>

                <!-- 棋盘 -->
                <div class="board-container">
                    <div id="board" class="board black-next"></div>
                </div>

                <!-- 按钮组 -->
                <div class="flex gap-4">
                    <button id="restart-btn" 
                            class="px-5 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white 
                                   rounded-xl hover:from-blue-500 hover:to-blue-600 
                                   transition-all duration-300 font-medium shadow-lg 
                                   hover:shadow-blue-500/25 active:scale-95">
                        重新开始
                    </button>
                    <button id="undo-btn" 
                            class="px-5 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white 
                                   rounded-xl hover:from-gray-500 hover:to-gray-600 
                                   transition-all duration-300 font-medium shadow-lg 
                                   hover:shadow-gray-500/25 active:scale-95
                                   disabled:opacity-50 disabled:cursor-not-allowed">
                        悔棋
                    </button>
                    <button id="reset-score-btn" 
                            class="px-5 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white 
                                   rounded-xl hover:from-red-500 hover:to-red-600 
                                   transition-all duration-300 font-medium shadow-lg 
                                   hover:shadow-red-500/25 active:scale-95">
                        重置记录
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新最近游玩时间戳
const updateRecentGame = () => {
    const recentGames = JSON.parse(localStorage.getItem('recentGames') || '[]');
    const gameId = location.pathname.includes('五子棋') ? 'gomoku' : 'minesweeper';
    
    const gameIndex = recentGames.findIndex(game => game.id === gameId);
    if (gameIndex !== -1) {
        recentGames[gameIndex].timestamp = Date.now();
        localStorage.setItem('recentGames', JSON.stringify(recentGames));
    }
};
updateRecentGame();
        class Gomoku {
            constructor() {
                this.size = 15;
                this.boardSize = 584;
                this.padding = 20;
                this.cellSize = (this.boardSize - this.padding * 2) / (this.size - 1);
                this.board = Array(this.size).fill().map(() => Array(this.size).fill(null));
                this.currentPlayer = 'black';
                this.gameOver = false;
                this.moves = [];
                this.wins = this.loadWinRecord();
                this.undoCount = { black: 3, white: 3 };
                this.timers = { black: 0, white: 0 };
                this.init();
                this.setupEventListeners();
                this.startTimer();
                this.updateUndoDisplay();
                this.updateWinDisplay();
                this.setupColorSelection();
            }

            init() {
                const board = document.getElementById('board');
                board.innerHTML = '';
                
                // 绘制网格线
                for (let i = 0; i < this.size; i++) {
                    const vertical = document.createElement('div');
                    vertical.className = 'grid-line vertical';
                    vertical.style.left = `${this.padding + i * this.cellSize}px`;
                    board.appendChild(vertical);

                    const horizontal = document.createElement('div');
                    horizontal.className = 'grid-line horizontal';
                    horizontal.style.top = `${this.padding + i * this.cellSize}px`;
                    board.appendChild(horizontal);
                }

                // 创建交叉点
                for (let y = 0; y < this.size; y++) {
                    for (let x = 0; x < this.size; x++) {
                        const intersection = document.createElement('div');
                        intersection.className = 'intersection';
                        intersection.dataset.x = x;
                        intersection.dataset.y = y;
                        intersection.style.left = `${this.padding + x * this.cellSize}px`;
                        intersection.style.top = `${this.padding + y * this.cellSize}px`;
                        board.appendChild(intersection);
                    }
                }

                // 添加星位点
                const starPoints = [
                    [3, 3], [11, 3], [7, 7], [3, 11], [11, 11]
                ];
                starPoints.forEach(([x, y]) => {
                    const point = document.createElement('div');
                    point.className = 'star-point';
                    point.style.left = `${this.padding + x * this.cellSize}px`;
                    point.style.top = `${this.padding + y * this.cellSize}px`;
                    board.appendChild(point);
                });
            }

            setupEventListeners() {
                const board = document.getElementById('board');
                board.addEventListener('click', (e) => {
                    if (this.gameOver) return;
                    
                    const intersection = e.target.closest('.intersection');
                    if (!intersection) return;

                    const x = parseInt(intersection.dataset.x);
                    const y = parseInt(intersection.dataset.y);

                    if (this.board[y][x]) return;

                    this.makeMove(x, y);
                });

                document.getElementById('restart-btn').addEventListener('click', () => {
                    this.restart();
                });

                document.getElementById('undo-btn').addEventListener('click', () => {
                    this.undo();
                });

                // 添加重置记录按钮的事件监听器
                document.getElementById('reset-score-btn').addEventListener('click', () => {
                    if (confirm('确定要重置所有胜利记录吗？')) {
                        this.resetWinRecord();
                    }
                });
            }

            makeMove(x, y) {
                this.board[y][x] = this.currentPlayer;
                this.moves.push({x, y, player: this.currentPlayer});

                const piece = document.createElement('div');
                piece.className = `piece ${this.currentPlayer}`;
                const intersection = document.querySelector(
                    `.intersection[data-x="${x}"][data-y="${y}"]`
                );
                intersection.appendChild(piece);

                if (this.checkWin(x, y)) {
                    this.gameOver = true;
                    this.wins[this.currentPlayer]++;
                    this.saveWinRecord();
                    this.updateWinDisplay();
                    
                    const statusText = `${this.currentPlayer === 'black' ? '黑' : '白'}胜`;
                    document.getElementById('status').textContent = statusText;
                    this.highlightWinningPieces(x, y);
                    this.updateBoardClass();
                    clearInterval(this.timerInterval);
                    return;
                }

                this.currentPlayer = this.currentPlayer === 'black' ? 'white' : 'black';
                this.updateBoardClass();
                this.updateUndoDisplay();
            }

            checkWin(x, y) {
                const directions = [
                    [[0, 1], [0, -1]],  // 垂直
                    [[1, 0], [-1, 0]],  // 水平
                    [[1, 1], [-1, -1]], // 对角线
                    [[1, -1], [-1, 1]]  // 反对角线
                ];

                return directions.some(direction => {
                    const count = 1 + 
                        this.countPieces(x, y, direction[0][0], direction[0][1]) +
                        this.countPieces(x, y, direction[1][0], direction[1][1]);
                    return count >= 5;
                });
            }

            countPieces(x, y, dx, dy) {
                const player = this.board[y][x];
                let count = 0;
                let currentX = x + dx;
                let currentY = y + dy;

                while (
                    currentX >= 0 && currentX < this.size &&
                    currentY >= 0 && currentY < this.size &&
                    this.board[currentY][currentX] === player
                ) {
                    count++;
                    currentX += dx;
                    currentY += dy;
                }

                return count;
            }

            highlightWinningPieces(x, y) {
                const directions = [
                    [[0, 1], [0, -1]],
                    [[1, 0], [-1, 0]],
                    [[1, 1], [-1, -1]],
                    [[1, -1], [-1, 1]]
                ];

                directions.forEach(direction => {
                    const count = 1 +
                        this.countPieces(x, y, direction[0][0], direction[0][1]) +
                        this.countPieces(x, y, direction[1][0], direction[1][1]);

                    if (count >= 5) {
                        // 高亮中心点
                        const centerPiece = document.querySelector(
                            `.intersection[data-x="${x}"][data-y="${y}"] .piece`
                        );
                        centerPiece.classList.add('winning-piece');

                        // 高亮两个方向
                        direction.forEach(([dx, dy]) => {
                            let currentX = x + dx;
                            let currentY = y + dy;

                            while (
                                currentX >= 0 && currentX < this.size &&
                                currentY >= 0 && currentY < this.size &&
                                this.board[currentY][currentX] === this.board[y][x]
                            ) {
                                const piece = document.querySelector(
                                    `.intersection[data-x="${currentX}"][data-y="${currentY}"] .piece`
                                );
                                piece.classList.add('winning-piece');
                                currentX += dx;
                                currentY += dy;
                            }
                        });
                    }
                });
            }

            restart() {
                this.board = Array(this.size).fill().map(() => Array(this.size).fill(null));
                this.currentPlayer = 'black';
                this.gameOver = false;
                this.moves = [];
                
                // 重置悔棋次数
                this.undoCount = {
                    black: 3,
                    white: 3
                };
                
                // 清除所有棋子
                document.querySelectorAll('.piece').forEach(piece => piece.remove());
                document.getElementById('status').textContent = '';

                // 重置计时器
                this.timers = { black: 0, white: 0 };
                this.updateTimerDisplay('black');
                this.updateTimerDisplay('white');
                clearInterval(this.timerInterval);
                this.startTimer();

                // 重置棋盘类名
                this.updateBoardClass();
                this.updateUndoDisplay();
            }

            undo() {
                if (this.moves.length === 0 || this.gameOver) return;
                
                const currentPlayerUndoCount = this.undoCount[this.currentPlayer];
                if (currentPlayerUndoCount <= 0) {
                    document.getElementById('status').textContent = '已达上限';
                    setTimeout(() => {
                        document.getElementById('status').textContent = '';
                    }, 2000);
                    return;
                }

                const lastMove = this.moves.pop();
                this.board[lastMove.y][lastMove.x] = null;
                
                const intersection = document.querySelector(
                    `.intersection[data-x="${lastMove.x}"][data-y="${lastMove.y}"]`
                );
                intersection.querySelector('.piece').remove();

                // 扣除悔棋次数
                this.undoCount[this.currentPlayer]--;
                this.updateUndoDisplay();

                this.currentPlayer = lastMove.player;
                this.updateStatus('');
                this.updateBoardClass();
            }

            startTimer() {
                this.timerInterval = setInterval(() => {
                    this.timers[this.currentPlayer]++;
                    this.updateTimerDisplay(this.currentPlayer);
                }, 1000);
            }

            updateTimerDisplay(player) {
                const minutes = Math.floor(this.timers[player] / 60);
                const seconds = this.timers[player] % 60;
                document.getElementById(`${player}-time`).textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }

            updateBoardClass() {
                const board = document.getElementById('board');
                board.className = `board ${this.currentPlayer}-next`;
                if (this.gameOver) {
                    board.classList.add('game-over');
                } else {
                    board.classList.remove('game-over');
                }
            }

            // 加载胜利记录
            loadWinRecord() {
                const record = localStorage.getItem('gomokuWins');
                return record ? JSON.parse(record) : { black: 0, white: 0 };
            }

            // 保存胜利记录
            saveWinRecord() {
                localStorage.setItem('gomokuWins', JSON.stringify(this.wins));
            }

            // 更新显示的胜利记录
            updateWinDisplay() {
                document.getElementById('black-wins').textContent = `胜: ${this.wins.black}`;
                document.getElementById('white-wins').textContent = `胜: ${this.wins.white}`;
            }

            // 重置胜利记录
            resetWinRecord() {
                this.wins = { black: 0, white: 0 };
                this.saveWinRecord();
                this.updateWinDisplay();
            }

            updateUndoDisplay() {
                document.getElementById('black-undo').textContent = `悔棋: ${this.undoCount.black}`;
                document.getElementById('white-undo').textContent = `悔棋: ${this.undoCount.white}`;
                
                // 更新悔棋按钮状态
                const undoBtn = document.getElementById('undo-btn');
                const currentPlayerUndoCount = this.undoCount[this.currentPlayer];
                
                if (this.moves.length === 0 || this.gameOver || currentPlayerUndoCount <= 0) {
                    undoBtn.disabled = true;
                    undoBtn.classList.add('opacity-50', 'cursor-not-allowed');
                } else {
                    undoBtn.disabled = false;
                    undoBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            }

            updateStatus(message) {
                const statusSpan = document.querySelector('#status span');
                statusSpan.textContent = message;
            }

            setupColorSelection() {
                const blackBtn = document.getElementById('select-black');
                const whiteBtn = document.getElementById('select-white');

                // 高亮当前选中的颜色
                const updateButtonStyles = () => {
                    if (this.currentPlayer === 'black') {
                        blackBtn.classList.add('ring-2', 'ring-yellow-400');
                        whiteBtn.classList.remove('ring-2', 'ring-yellow-400');
                    } else {
                        whiteBtn.classList.add('ring-2', 'ring-yellow-400');
                        blackBtn.classList.remove('ring-2', 'ring-yellow-400');
                    }
                };

                // 初始高亮黑棋
                updateButtonStyles();

                blackBtn.addEventListener('click', () => {
                    if (this.moves.length > 0 || this.gameOver) {
                        if (confirm('切换先手颜色将重新开始游戏，确定吗？')) {
                            this.currentPlayer = 'black';
                            this.restart();
                            updateButtonStyles();
                        }
                    } else {
                        this.currentPlayer = 'black';
                        this.updateBoardClass();
                        updateButtonStyles();
                    }
                });

                whiteBtn.addEventListener('click', () => {
                    if (this.moves.length > 0 || this.gameOver) {
                        if (confirm('切换先手颜色将重新开始游戏，确定吗？')) {
                            this.currentPlayer = 'white';
                            this.restart();
                            updateButtonStyles();
                        }
                    } else {
                        this.currentPlayer = 'white';
                        this.updateBoardClass();
                        updateButtonStyles();
                    }
                });
            }
        }

        // 启动游戏
        new Gomoku();
    </script>
</body>
</html>
