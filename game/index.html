<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小游戏合集</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        /* 动态背景样式 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #1a1c2c 0%, #2a3042 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(236, 72, 153, 0.08) 0%, transparent 50%);
            animation: gradientMove 20s ease infinite;
            z-index: -1;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239BA1AF' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 0.3;
            z-index: -1;
            animation: patternMove 120s linear infinite;
        }

        @keyframes gradientMove {
            0% {
                transform: scale(1) rotate(0deg);
            }
            50% {
                transform: scale(1.1) rotate(3deg);
            }
            100% {
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes patternMove {
            0% {
                transform: translateY(0) translateX(0);
            }
            50% {
                transform: translateY(-20px) translateX(20px);
            }
            100% {
                transform: translateY(0) translateX(0);
            }
        }

        /* 发光粒子效果 */
        .particle {
            position: fixed;
            pointer-events: none;
            border-radius: 50%;
            background: white;
            opacity: 0;
            z-index: -1;
        }

        @keyframes particleFade {
            0% {
                transform: scale(0);
                opacity: 0.5;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }

        /* 游戏卡片悬浮效果增强 */
        .game-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.05);
            overflow: hidden;
            position: relative;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .game-card:hover {
            transform: translateY(-5px) scale(1.02) rotateX(2deg) rotateY(-2deg);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 
                0 15px 30px rgba(0, 0, 0, 0.2),
                0 5px 15px rgba(59, 130, 246, 0.1);
        }

        .game-card::before {
            content: '';
            position: absolute;
            inset: 0;
            background: radial-gradient(
                800px circle at var(--mouse-x) var(--mouse-y),
                rgba(255, 255, 255, 0.06),
                transparent 40%
            );
            opacity: 0;
            transition: opacity 0.3s;
        }

        .game-card:hover::before {
            opacity: 1;
        }

        .game-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(125deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .game-card:hover::after {
            opacity: 1;
        }

        .game-icon {
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", sans-serif;
            font-size: 2.5rem;
            line-height: 1;
        }
        
        .preview-image {
            height: 180px;
            background-size: cover;
            background-position: center;
            transition: all 0.5s ease;
            position: relative;
        }
        
        .game-card:hover .preview-image {
            transform: scale(1.05);
        }
        
        .preview-overlay {
            position: absolute;
            inset: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 50%);
        }
        
        /* 添加动画效果 */
        @keyframes pulse-glow {
            0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
            100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }
        
        .play-button {
            animation: pulse-glow 2s infinite;
            transition: all 0.3s ease;
        }
        
        .game-card:hover .play-button {
            transform: scale(1.1);
        }

        /* 搜索框样式 */
        .search-container {
            position: relative;
            margin-bottom: 2rem;
        }
        
        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.75rem;
            color: white;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            outline: none;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }
        
        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            pointer-events: none;
        }

        /* 无结果提示样式 */
        .no-results {
            text-align: center;
            padding: 2rem;
            color: #94a3b8;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
        }

        /* 分页样式 */
        .pagination-button {
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .pagination-button:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
        }
        
        .pagination-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .pagination-button.active {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.3);
            color: #60a5fa;
        }

        /* 游戏统计样式 */
        .stats-badge {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(4px);
        }

        /* 分类标签样式 */
        .category-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 2rem;
        }
        
        .category-tag {
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.7);
        }
        
        .category-tag:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
        }
        
        .category-tag.active {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.3);
            color: #60a5fa;
        }

        /* 游戏标签样式 */
        .game-tag {
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>
<body class="flex items-center justify-center p-4"> 
    <div class="container max-w-4xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-200 to-yellow-500 mb-4">
                小游戏合集
            </h1>
            <p class="text-gray-400 mb-2">选择一个游戏开始玩吧！</p>
            <div class="stats-badge inline-flex items-center px-3 py-1 rounded-full text-sm text-gray-300">
                <span id="game-count">3</span> 个游戏
            </div>
        </div>

        <!-- 分类标签 -->
        <div class="category-tags">
            <button class="category-tag active" data-category="all">全部</button>
            <button class="category-tag" data-category="board">棋类游戏</button>
            <button class="category-tag" data-category="puzzle">益智解谜</button>
            <button class="category-tag" data-category="strategy">策略游戏</button>
        </div>

        <!-- 搜索框 -->
        <div class="search-container">
            <input type="text" 
                   id="game-search" 
                   class="search-input" 
                   placeholder="搜索游戏..."
                   autocomplete="off">
            <svg class="search-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
        </div>
        
        <!-- 最近游玩区域 -->
        <div id="recent-games-section" class="recent-section mb-10 p-4 hidden">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-white">最近游玩</h2>
                <button id="clear-recent" class="text-gray-400 text-sm hover:text-gray-300 transition-colors">
                    清除记录
                </button>
            </div>
            <div id="recent-games-list" class="grid grid-cols-2 md:grid-cols-4 gap-3">
                <!-- 最近游玩的游戏将通过JS动态添加 -->
            </div>
        </div>

        <!-- 游戏列表 -->
        <div id="games-grid" class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <!-- 五子棋 -->
            <a href="gomoku.html" class="game-card rounded-2xl overflow-hidden shadow-lg group" 
               data-game="gomoku" 
               data-name="五子棋" 
               data-icon="⚫" 
               data-image="../images/gomoku.jpg"
               data-categories="board,strategy">
                <div class="preview-image" style="background-image: url('../images/gomoku.jpg');">
                    <div class="preview-overlay"></div>
                    <div class="absolute top-4 left-4 bg-black/50 rounded-full p-2">
                        <div class="game-icon">⚫</div>
                    </div>
                    <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <div class="play-button bg-blue-500/80 text-white rounded-full w-16 h-16 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-white mb-2">五子棋</h2>
                    <p class="text-gray-400 mb-4">经典的双人对战游戏，谁先连成五子谁就赢</p>
                    <div class="flex flex-wrap gap-2 mb-3">
                        <span class="game-tag">棋类游戏</span>
                        <span class="game-tag">策略游戏</span>
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 rounded-full bg-blue-600/20 text-blue-400 text-xs">支持悔棋</span>
                        <span class="px-3 py-1 rounded-full bg-blue-600/20 text-blue-400 text-xs">计时</span>
                        <span class="px-3 py-1 rounded-full bg-blue-600/20 text-blue-400 text-xs">胜利统计</span>
                    </div>
                </div>
            </a>

            <!-- 扫雷 -->
            <a href="minesweeper.html" class="game-card rounded-2xl overflow-hidden shadow-lg group"
               data-game="minesweeper" 
               data-name="扫雷" 
               data-icon="💣" 
               data-image="../images/minesweeper.jpg"
               data-categories="puzzle,strategy">
                <div class="preview-image" style="background-image: url('../images/minesweeper.jpg');">
                    <div class="preview-overlay"></div>
                    <div class="absolute top-4 left-4 bg-black/50 rounded-full p-2">
                        <div class="game-icon">💣</div>
                    </div>
                    <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <div class="play-button bg-blue-500/80 text-white rounded-full w-16 h-16 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-white mb-2">扫雷</h2>
                    <p class="text-gray-400 mb-4">考验逻辑推理能力的经典游戏，小心地雷！</p>
                    <div class="flex flex-wrap gap-2 mb-3">
                        <span class="game-tag">益智解谜</span>
                        <span class="game-tag">策略游戏</span>
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 rounded-full bg-blue-600/20 text-blue-400 text-xs">三种难度</span>
                        <span class="px-3 py-1 rounded-full bg-blue-600/20 text-blue-400 text-xs">最佳记录</span>
                        <span class="px-3 py-1 rounded-full bg-blue-600/20 text-blue-400 text-xs">标记功能</span>
                    </div>
                </div>
            </a>
        </div>

        <!-- 俄罗斯方块 -->
<a href="tetris.html" class="game-card rounded-2xl overflow-hidden shadow-lg group"
data-game="tetris" 
data-name="俄罗斯方块" 
data-icon="🎮" 
data-image="../images/tetris.jpg"
data-categories="puzzle,strategy">
 <div class="preview-image" style="background-image: url('../images/tetris.jpg');">
     <div class="preview-overlay"></div>
     <div class="absolute top-4 left-4 bg-black/50 rounded-full p-2">
         <div class="game-icon">🎮</div>
     </div>
     <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
         <div class="play-button bg-blue-500/80 text-white rounded-full w-16 h-16 flex items-center justify-center">
             <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
             </svg>
         </div>
     </div>
 </div>
 <div class="p-6">
     <h2 class="text-2xl font-bold text-white mb-2">俄罗斯方块</h2>
     <p class="text-gray-400 mb-4">经典的俄罗斯方块游戏，考验你的空间思维能力</p>
     <div class="flex flex-wrap gap-2 mb-3">
         <span class="game-tag">益智解谜</span>
         <span class="game-tag">策略游戏</span>
     </div>
     <div class="flex flex-wrap gap-2">
         <span class="px-3 py-1 rounded-full bg-blue-600/20 text-blue-400 text-xs">经典玩法</span>
         <span class="px-3 py-1 rounded-full bg-blue-600/20 text-blue-400 text-xs">难度递增</span>
         <span class="px-3 py-1 rounded-full bg-blue-600/20 text-blue-400 text-xs">分数系统</span>
     </div>
 </div>
</a>

        <!-- 分页控件 -->
        <div id="pagination" class="flex justify-center items-center gap-2 mt-8 mb-4">
            <button class="pagination-button px-4 py-2 rounded-lg text-white disabled:opacity-50" 
                    id="prev-page" disabled>
                上一页
            </button>
            <div class="flex items-center gap-1" id="page-numbers">
                <button class="pagination-button w-10 h-10 rounded-lg text-white active">1</button>
            </div>
            <button class="pagination-button px-4 py-2 rounded-lg text-white disabled:opacity-50" 
                    id="next-page" disabled>
                下一页
            </button>
        </div>

        <!-- 无搜索结果提示 -->
        <div id="no-results" class="no-results hidden">
            <p class="text-lg mb-2">没有找到相关游戏</p>
            <p class="text-sm">试试其他关键词？</p>
        </div>

        <!-- 页脚 -->
        <footer class="mt-12 text-center text-gray-500 text-sm">
            <p>使用现代浏览器以获得最佳体验</p>
        </footer>
    </div>

    <script>
        class GameHistory {
            constructor() {
                this.recentGames = this.loadRecentGames();
                this.maxRecentGames = 4; // 最多显示4个最近游玩
                this.init();
            }

            init() {
                this.setupGameLinks();
                this.setupClearButton();
                this.updateRecentGamesDisplay();
            }

            loadRecentGames() {
                const saved = localStorage.getItem('recentGames');
                return saved ? JSON.parse(saved) : [];
            }

            saveRecentGames() {
                localStorage.setItem('recentGames', JSON.stringify(this.recentGames));
            }

            addRecentGame(gameData) {
                // 移除已存在的相同游戏
                this.recentGames = this.recentGames.filter(game => game.id !== gameData.id);
                
                // 添加到开头
                this.recentGames.unshift({
                    id: gameData.id,
                    name: gameData.name,
                    icon: gameData.icon,
                    image: gameData.image,
                    url: gameData.url,
                    timestamp: Date.now()
                });

                // 保持最大数量限制
                if (this.recentGames.length > this.maxRecentGames) {
                    this.recentGames.pop();
                }

                this.saveRecentGames();
                this.updateRecentGamesDisplay();
            }

            clearRecentGames() {
                this.recentGames = [];
                this.saveRecentGames();
                this.updateRecentGamesDisplay();
            }

            setupGameLinks() {
                document.querySelectorAll('.game-card').forEach(card => {
                    card.addEventListener('click', (e) => {
                        const gameData = {
                            id: card.dataset.game,
                            name: card.dataset.name,
                            icon: card.dataset.icon,
                            image: card.dataset.image,
                            url: card.href
                        };
                        this.addRecentGame(gameData);
                    });
                });
            }

            setupClearButton() {
                document.getElementById('clear-recent').addEventListener('click', (e) => {
                    e.preventDefault();
                    if (confirm('确定要清除最近游玩记录吗？')) {
                        this.clearRecentGames();
                    }
                });
            }

            updateRecentGamesDisplay() {
                const section = document.getElementById('recent-games-section');
                const container = document.getElementById('recent-games-list');
                
                if (this.recentGames.length === 0) {
                    section.classList.add('hidden');
                    return;
                }

                section.classList.remove('hidden');
                container.innerHTML = this.recentGames.map(game => this.createRecentGameElement(game)).join('');
            }

            createRecentGameElement(game) {
                const timeAgo = this.getTimeAgo(game.timestamp);
                return `
                    <a href="${game.url}" class="recent-game rounded-xl p-3 flex items-center gap-3">
                        <div class="w-10 h-10 rounded-full bg-black/30 flex items-center justify-center">
                            <span class="text-2xl">${game.icon}</span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="text-white font-medium truncate">${game.name}</div>
                            <div class="text-gray-400 text-xs">${timeAgo}</div>
                        </div>
                    </a>
                `;
            }

            getTimeAgo(timestamp) {
                const seconds = Math.floor((Date.now() - timestamp) / 1000);
                
                if (seconds < 60) return '刚刚';
                
                const minutes = Math.floor(seconds / 60);
                if (minutes < 60) return `${minutes}分钟前`;
                
                const hours = Math.floor(minutes / 60);
                if (hours < 24) return `${hours}小时前`;
                
                const days = Math.floor(hours / 24);
                if (days < 30) return `${days}天前`;
                
                return '很久以前';
            }
        }

        class GameManager {
            constructor() {
                this.gameHistory = new GameHistory();
                this.gamesPerPage = 6;
                this.currentPage = 1;
                this.totalGames = document.querySelectorAll('.game-card').length;
                this.totalPages = Math.ceil(this.totalGames / this.gamesPerPage);
                this.currentCategory = 'all';
                
                this.setupSearch();
                this.setupPagination();
                this.setupCategories();
                this.updateGameCount();
            }

            setupSearch() {
                const searchInput = document.getElementById('game-search');
                const gamesGrid = document.getElementById('games-grid');
                const noResults = document.getElementById('no-results');
                const gameCards = Array.from(document.querySelectorAll('.game-card'));

                searchInput.addEventListener('input', (e) => {
                    const searchTerm = e.target.value.toLowerCase().trim();
                    let hasResults = false;
                    let visibleGames = 0;

                    gameCards.forEach(card => {
                        const gameName = card.dataset.name.toLowerCase();
                        const isMatch = gameName.includes(searchTerm);
                        card.classList.toggle('hidden', !isMatch);
                        if (isMatch) {
                            hasResults = true;
                            visibleGames++;
                        }
                    });

                    // 更新游戏计数和分页状态
                    this.updateGameCount(visibleGames);
                    document.getElementById('pagination').classList.toggle('hidden', !hasResults || searchTerm);
                    gamesGrid.classList.toggle('hidden', !hasResults);
                    noResults.classList.toggle('hidden', hasResults);
                });

                // 添加快捷键支持
                document.addEventListener('keydown', (e) => {
                    // 按下 / 键时聚焦搜索框
                    if (e.key === '/' && document.activeElement !== searchInput) {
                        e.preventDefault();
                        searchInput.focus();
                    }
                    // 按下 ESC 键时清空搜索框
                    if (e.key === 'Escape' && document.activeElement === searchInput) {
                        searchInput.value = '';
                        searchInput.dispatchEvent(new Event('input'));
                        searchInput.blur();
                    }
                });
            }

            setupPagination() {
                const prevButton = document.getElementById('prev-page');
                const nextButton = document.getElementById('next-page');
                const pageNumbers = document.getElementById('page-numbers');

                // 当前只有一页，隐藏分页控件
                if (this.totalPages <= 1) {
                    document.getElementById('pagination').classList.add('hidden');
                    return;
                }

                // 更新分页按钮状态
                this.updatePaginationState = () => {
                    prevButton.disabled = this.currentPage === 1;
                    nextButton.disabled = this.currentPage === this.totalPages;
                    
                    // 更新页码按钮
                    pageNumbers.innerHTML = '';
                    for (let i = 1; i <= this.totalPages; i++) {
                        const button = document.createElement('button');
                        button.className = `pagination-button w-10 h-10 rounded-lg text-white 
                                          ${i === this.currentPage ? 'active' : ''}`;
                        button.textContent = i;
                        button.onclick = () => this.goToPage(i);
                        pageNumbers.appendChild(button);
                    }
                };

                prevButton.onclick = () => this.goToPage(this.currentPage - 1);
                nextButton.onclick = () => this.goToPage(this.currentPage + 1);

                this.updatePaginationState();
            }

            goToPage(page) {
                if (page < 1 || page > this.totalPages) return;
                this.currentPage = page;
                this.updatePaginationState();
                
                // 更新游戏显示
                const start = (page - 1) * this.gamesPerPage;
                const end = start + this.gamesPerPage;
                
                Array.from(document.querySelectorAll('.game-card')).forEach((card, index) => {
                    card.classList.toggle('hidden', index < start || index >= end);
                });
            }

            updateGameCount(count = null) {
                const gameCount = document.getElementById('game-count');
                gameCount.textContent = count !== null ? count : this.totalGames;
            }

            setupCategories() {
                const categoryTags = document.querySelectorAll('.category-tag');
                
                categoryTags.forEach(tag => {
                    tag.addEventListener('click', () => {
                        // 更新标签状态
                        categoryTags.forEach(t => t.classList.remove('active'));
                        tag.classList.add('active');
                        
                        // 过滤游戏
                        this.currentCategory = tag.dataset.category;
                        this.filterGames();
                    });
                });
            }

            filterGames() {
                const searchTerm = document.getElementById('game-search').value.toLowerCase().trim();
                let visibleGames = 0;
                
                Array.from(document.querySelectorAll('.game-card')).forEach(card => {
                    const gameName = card.dataset.name.toLowerCase();
                    const categories = card.dataset.categories.split(',');
                    
                    const matchesSearch = gameName.includes(searchTerm);
                    const matchesCategory = this.currentCategory === 'all' || 
                                         categories.includes(this.currentCategory);
                    
                    const isVisible = matchesSearch && matchesCategory;
                    card.classList.toggle('hidden', !isVisible);
                    
                    if (isVisible) visibleGames++;
                });

                // 更新游戏计数和分页
                this.updateGameCount(visibleGames);
                this.updatePagination();
                
                // 显示/隐藏无结果提示
                const hasResults = visibleGames > 0;
                document.getElementById('games-grid').classList.toggle('hidden', !hasResults);
                document.getElementById('no-results').classList.toggle('hidden', hasResults);
            }
        }

        // 添加动态粒子效果
        class ParticleEffect {
            constructor() {
                this.particles = [];
                this.maxParticles = 30;
                this.init();
            }

            init() {
                document.addEventListener('mousemove', (e) => {
                    if (Math.random() > 0.85) { // 控制粒子生成频率
                        this.createParticle(e.clientX, e.clientY);
                    }
                });
            }

            createParticle(x, y) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                const size = Math.random() * 10 + 5;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.left = `${x}px`;
                particle.style.top = `${y}px`;
                
                document.body.appendChild(particle);
                
                // 添加动画
                particle.animate([
                    {
                        transform: 'scale(0)',
                        opacity: 0.5
                    },
                    {
                        transform: 'scale(1)',
                        opacity: 0
                    }
                ], {
                    duration: 1000,
                    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
                });

                // 清理粒子
                setTimeout(() => {
                    particle.remove();
                }, 1000);
            }
        }

        // 初始化游戏历史记录
        new GameHistory();

        // 初始化游戏管理器
        new GameManager();

        // 初始化粒子效果
        new ParticleEffect();
    </script>
</body>
</html>