***************:qqmusic-android-project/module-app.git
***************:nickoli/dex2oat-pro.git
cnb.tmeoa.com/karaoke_harmonyos/Karaoke-Kuikly.git
***************:game-atum/Atum_Android.git
git.woa.com:musicfe-c/X-Projects/qmkuikly.git
tcode.tmeoa.com/tme-tech/front-end/effects/tekrender.git
https://git.woa.com/ibg-joox/JOOX_Android.git
git.woa.com/qqmusic_innovation/open_api_sdk/Android_Demo.git
***************:qqmusic_innovation/QQMusicCar.git
https://git.woa.com/tme-kuikly/tme-kuikly
git.woa.com:karaoke/karaoke-android.git
git.woa.com/ibg-joox/JOOX_Android
git.woa.com/qqmusic_android/QQMusicMediaPlayer.git
git.woa.com/tme-kuikly/tme-kuikly
git.woa.com/wesing/wesing_international_android.git
***************:karaoke_tv/ktv_sdk.git
***************:qqmusic_innovation/qqmusic_lite.git
https://git.woa.com/karaoke/KMP/lib_design.git
https://git.woa.com/wesing/tme-lib.git
https://git.woa.com/qitianliu/AndroidKnowledgeNew.git
git.woa.com/qqmusic_innovation/QQMusicCar.git
git.woa.com/karaoke/karaoke-android.git
git.woa.com/qqmusic-android-project/module-app.git
git.woa.com/qqmusic_innovation/qqmusic_lite.git
***************:qqmusic_innovation/open_api_sdk/Android.git
***************:qqmusic_innovation/open_api_sdk/Android_Demo.git
git.woa.com:wesing/wesing_international_android.git
git.woa.com/musicfe-c/X-Projects/qmkuikly.git
git.woa.com/wesing/tme-lib.git
https://git.woa.com/tme_develop/FrontAIFramework.git
git.woa.com/stevenji/knative-android-karaoke.git
http://tcode.tmeoa.com/tme-tech/front-end/effects/tekrender.git
github.com/********/my-dot.git
***************:karaoke/Hippy-loader.git
***************:game-atum/atum-ability.git
git.woa.com/Short-Video/short-video-android.git
cnb.tmeoa.com/karaoke_harmonyos/karaoke-ktvroom.git
git.woa.com/Kuikly/KuiklyDemo.git
https://git.woa.com/qqmusic_innovation/********TV/Android/QQMusicTV_v2.git
git.woa.com:MusicLive/ModuleLive.git
git.woa.com:karaoke/lib_audiobase.git
https://git.woa.com/ibg-joox/JOOX_Kuikly.git
cnb.tmeoa.com/bsonyu/KMPModuleTemplate.git
https://git.woa.com/qqmusic_innovation/bluetooth-earphone-sdk.git
git.woa.com:tme_develop/TMECodec/TMECodec.git
git.woa.com/Short-Video/qmdj-kuikly/kuikly.git
git.woa.com/karaoke/HarmonyOS/Karaoke-Kuikly.git
git.woa.com:tme-kuikly/knative-android-karaoke.git
git.woa.com:qqmusic_innovation/SdkMethodMonitor.git
https://cnb.tmeoa.com/FireEye/FireEye-Android.git
https://git.woa.com/qqmusic_android/QPlay3.git
https://git.woa.com/karaoke_tv/karaoke_tv_android.git
git.woa.com:karaoke_tools/KMSdkProject.git
git.woa.com/tme_develop/TMECodec/TMECodec.git
git.woa.com/karaoke/KMP/lib_design.git
***************:qqmusic-android-project/framework-cyclone.git
***************:qqmusic_innovation/module_playback_audio.git
tcode.tmeoa.com/tme-tech/front-end/player/qqmusic-android-module-player.git
***************:qqmusic-android-project/skinEngine.git
git.woa.com:coryshuang/Jce2Kotlin.git
***************:karaoke/lib_recordsdk.git
***************:qqmusic_innovation/common.git
github.com:sickworm/jugg.git
https://git.woa.com/qqmusic_android/QQMusicMediaPlayer.git
***************:musicfe-c/X-Projects/TMEPay_Android.git
git.woa.com:coryshuang/TME-UnionMic-Android.git
https://git.woa.com/qqmusic-android-project/framework-qz-downloader.git
git.woa.com/karaoke/KMP/lib-live-utils.git
***************:qqmusic_innovation/open_api_sdk/Linux.git
git.woa.com/karaoke_tools/KaraokeDtsProject.git
***************:kevinylzhao/kuikly-seek.git
***************:tme-kuikly/kuikly-loader.git
git.woa.com:karaoke/KMP/TME-Pay-Android.git
https://git.woa.com/karaoke/KMP/lib-record.git
git.woa.com/qqmusic_innovation/********TV/Android/QQMusicTV_v2.git
tcode.tmeoa.com/tme/tme_interact/karaoke-android/kmp-karaoke-ktv-room.git
git.woa.com:qqmusic_innovation/login.git
***************:musicfe-c/X-Projects/KuiklyCore.git
https://git.woa.com/karaoke/KMP/lib-audiobase.git
***************:kg-flutter-library/kg-interface.git
***************:karaoke_tv/tv_ui_sdk.git
***************:Kuikly/KuiklyDemo.git
git.woa.com:tme_develop/TMEX/tmex.git
git.woa.com:tme_develop/TMERTCKit/TMERTCKit_Android.git
git.woa.com/coryshuang/TME-Kuikly.git
git.woa.com:pyramid_com/forest-react-material.git
git.woa.com:hqzheng/iot-hive-view.git
git.woa.com/SuperSound/TMESSEffect.git
***************:rockyzzhao/SharedKuikly.git
git.coding.tmeoa.com/codingcorp/fireeye/FireEye-Android.git
***************:game-atum/Atum_Android_Launcher.git
https://github.com/Tencent/vap.git
**************:byhook/maple-studio.git
https://git.woa.com/Short-Video/short-video-android.git
***************:tinguo/MusicHealing.git
git.coding.tmeoa.com/codingcorp/fireeye/XPM-Android.git
https://git.coding.tmeoa.com/codingcorp/fireeye/FireEye-Android.git
git.woa.com:tme-kuikly/kg-interface-kuikly.git
git.woa.com/symphony/tencentmusic_adsdk_android.git
https://tcode.tmeoa.com/tme-kuikly/qmkuikly
***************:karaoke/KMP/lib-tme-live-media-framework.git
cnb.tmeoa.com/kgtv/ktv_sdk.git
https://git.woa.com/********Android-Modular-Lab/qqmusic-git-hook.git
***************:xingyaan/GradlePluginTest.git
github.com/carlrobertoh/CodeGPT.git
github.com:byhook/SafeBox.git
***************:Kuikly/KuiklyJceTools.git
https://git.woa.com/qqmusic-android-project/DTSHeadphoneXMobileApp.git
git.woa.com/********Android-Modular-Lab/Lightning.git
git.woa.com:karaoke/KMP/component-giftpanel.git
***************:qqmusic_innovation/QQMusicWatch.git
***************:karaoke/KMP/lib-encode.git
git.woa.com:musicfe-c/X-Projects/JS2Kuikly-Forest.git
https://github.com/zerosnow/FoundationPlan.git
***************:tme_develop/TMERTCKit/TMELiveMediaFramework_Android.git
https://github.com/android/camera-samples.git
https://git.woa.com/karaoke_tools/lib_karaoke.git
github.com:byhook/maple-studio.git
git.woa.com:tme-kuikly/kuiklyx-bridge.git
git.woa.com/karaoke/KMP/lib-report.git
http://tcode.tmeoa.com/tme-tech/front-end/player/qqmusic-android-module-player.git
git.woa.com/qqmusic-android-project/framework-cyclone.git
git.woa.com:karaoke/KMP/lib-across-jce.git
git.coding.tmeoa.com/codingcorp/tme-copilot/TME-Copilot-JetBrains.git
https://github.com/NoneGG/aredis.git
**************:byhook/SafeBox.git
git.woa.com:tme_develop/X5Installer.git
https://git.woa.com/Short-Video/qmdj-kuikly/kuikly.git
***************:musicfe-c/X-Projects/XSonic_Android.git
***************:karaoke/lib_rtc_chain.git
git.woa.com/qqmusic-android-project/DTSHeadphoneXMobileApp.git
https://github.com/********/kotlinx-build-config.git
https://git.woa.com/ericzwhuang/Fragment.git
https://git.woa.com/qitianliu/QQMusicStarter.git
***************:Kuikly/KuiklyCore.git
https://git.woa.com/qqmusic-android-project/framework-common.git
https://git.woa.com/qqmusic_innovation/QPlay-IPC/Android/QQMusic_Innovation_QPlay_AIDL_OpenID_Demo.git
git.woa.com/qqmusic_innovation/open_api_sdk/Android.git
***************:game-atum/magicbrush_android.git
git.woa.com/wormchen/application1.git
https://github.com/Tencent/tinker.git
cnb.tmeoa.com/kgtv/karaoke_tv_android.git
***************:musicfe-c/X-Projects/Leeuwenhoek_Android.git
git.woa.com/qqmusic_innovation/QQMusicTV.git
https://github.com/baidu/lac.git
