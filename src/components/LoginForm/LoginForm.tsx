import React, { useState } from 'react';
import './LoginForm.css';

interface LoginFormProps {
  onSubmit: (username: string, password: string) => void;
  loading?: boolean;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit, loading = false }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 基本验证
    if (!username.trim()) {
      setError('请输入用户名');
      return;
    }
    
    if (!password) {
      setError('请输入密码');
      return;
    }
    
    setError(null);
    onSubmit(username, password);
  };

  return (
    <div className="login-form-container">
      <div className="login-form-card">
        <h2 className="login-form-title">登录</h2>
        
        {error && <div className="login-form-error">{error}</div>}
        
        <form onSubmit={handleSubmit} className="login-form">
          <div className="login-form-field">
            <label htmlFor="username">用户名</label>
            <input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="请输入用户名"
              disabled={loading}
            />
          </div>
          
          <div className="login-form-field">
            <label htmlFor="password">密码</label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="请输入密码"
              disabled={loading}
            />
          </div>
          
          <div className="login-form-actions">
            <button 
              type="submit" 
              className="login-form-button"
              disabled={loading}
            >
              {loading ? '登录中...' : '登录'}
            </button>
          </div>
          
          <div className="login-form-links">
            <a href="#forgot-password" className="login-form-link">忘记密码?</a>
            <a href="#register" className="login-form-link">注册新账号</a>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginForm;