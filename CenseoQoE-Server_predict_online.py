# -*- coding: utf-8 -*-
# @Time    : 2020/12/3 7:16 下午
# <AUTHOR> shaoguowen
# @Email   : <EMAIL>
# @FileName: CenseoQoE_predict_online.py
# @Software: PyCharm
import os
import time
import requests
import json
#import pandas as pd
#import numpy as np
#from scipy import stats
#import matplotlib.pyplot as plt
import shutil
#import pandas as pd


def video_quality_predict(vpath_dst, vpath_ref=None, backbone_name="resnet18", model_name=None, scene_name=None,
                          sample_fps=1, is_fr=False):
    """
    CenseoQoE 画质评价在线服务调用
    :param vpath_dst: 失真视频
    :param vpath_ref: 参考视频
    :param token:
    :return:
    """
    assert vpath_dst and os.path.exists(vpath_dst), "vpath_dst must exist"
    if is_fr:
        assert vpath_ref and os.path.exists(vpath_ref), "vpath_ref must exist in FR mode"
    ## 模型预测的配置
    model_param = {"backbone": backbone_name, "sample_fps": sample_fps, "scene_name": scene_name, "fr": is_fr}
    print(model_param)
    data = {
        "model_name": model_name,
        "model_param": json.dumps(model_param),
        "is_send": 0
    }
    file_rb_dst = open(vpath_dst, 'rb')
    files = {'dst_file': file_rb_dst}
    if is_fr:
        file_rb_ref = open(vpath_ref, 'rb')
        files.update({"ref_file": file_rb_ref})

    # host = "*************"
    # port = 80

    host = "************"
    port = 80

    # host = "127.0.0.1"
    # port = 5001

    path = "/api/predict_v2"
    scheme = "http"
    try:
        print ("http://************:80/api/predict_v2")
        st = time.time()
        r = requests.post("http://************:80/api/predict_v2", data=data, files=files)
        print(r.status_code)
        print(time.time() - st)
        return r.json()
    except Exception as e:
        import traceback
        print(traceback.format_exc())
    finally:
        file_rb_dst.close()
        if is_fr:
            file_rb_ref.close()


if __name__ == '__main__':
    
    '''
    # 模型输入的其他参数
    model_name = "dl_ugc"

    # Oteam的UGC数据
    backbone_name = "pplcnet25"
    scene_name = "general_v3"
    '''
    model_name = "dl_games"
    backbone_name = "resnet18"
    scene_name = "compressed_v1"
    # 如果想对码率、分辨率等变化比较敏感，则使用以下参数
    # backbone_name = "resnet18"
    # scene_name = "general_v2"

    sample_fps = 2  # 每秒预测多少帧
    is_fr = False

    #vpath = "/Users/<USER>/Downloads/high-1-2.mp4"

    video_path = r"/Users/<USER>/Desktop/kg_video" \
                 r"" \
                 r"/"
    for root, dirs, files in os.walk(video_path):
        for fl in files:
            #if fl.endswith(".mp4"):
            vpath = os.path.join(root,fl)
            print(vpath)
            ret = video_quality_predict(vpath, vpath_ref=None, is_fr=is_fr, backbone_name=backbone_name, model_name=model_name,
                                scene_name=scene_name,
                                sample_fps=sample_fps)
            print ret
