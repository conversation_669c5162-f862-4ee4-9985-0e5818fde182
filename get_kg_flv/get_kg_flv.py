#!/usr/bin/env python
# -*- coding: utf-8 -*-


import os
import sys
import json
import time
from datetime import datetime, date, timedelta
import requests
from contextlib import closing

MAX_FILE_SIZE = 2 * 1024 * 1024

def download(save_path, filename, url):
    #file_name = time.strftime('%Y_%m_%d_%H_%M_%S.flv', time.localtime(time.time()))
    file_name = filename+".flv"
    try:
        with closing(requests.get(url, stream=True)) as response:
            chunk_size = 1024  # 单次请求最大值
            # content_size = int(response.headers['content-length'])  # 内容体总大小
            print(save_path + "/"+file_name)
            with open(save_path + "/"+file_name, "wb") as f:
                sum = 0
                for data in response.iter_content(chunk_size=chunk_size):
                    f.write(data)
                    sum += chunk_size
                    # 如果超过下载文件最大值则停止下载
                    if sum >= MAX_FILE_SIZE:
                        print('到达' + str(MAX_FILE_SIZE / 1024 / 1024) + 'M，停止下载')
                        return save_path + "/"+file_name
        return None
    except Exception as e:
        print (str(e))
        return None


def process(url, resolution, level):
    print url, resolution, level
    save_path = os.getcwd()
    if resolution=="720":
        if level == 0:
            url = url.replace(".flv", "_a720.flv")
            save_path = save_path + "/" + "720p"
        elif level == 1:
            url = url.replace(".flv", "_a720h265V2.flv")
            save_path = save_path + "/" + "720p265v1"
        elif level == 2:
            url = url.replace(".flv", "_a720h265.flv")
            save_path = save_path + "/" + "720p265v2"
        else:
            url = url.replace(".flv", "_a720h265V3.flv")
            save_path = save_path + "/" + "720p265v3"
    elif resolution=="1080":
        if level == 0:
            url = url.replace(".flv", "_a1080v2.flv")
            save_path = save_path + "/" + "1080p4M"
        elif level == 1:
            url = url.replace(".flv", "_a1080h265a.flv")
            save_path = save_path + "/" + "1080p265v1"
        elif level == 2:
            url = url.replace(".flv", "_a1080h265.flv")
            save_path = save_path + "/" + "1080p265v2"
        else:
            url = url.replace(".flv", "_a1080h265b.flv")
            save_path = save_path + "/" + "1080p265v3"
    else:
    	print "error param"
    	return 


    if not os.path.exists(save_path):
        os.makedirs(save_path) 
        print save_path+' 创建成功'
    count = 0
    while count < 50 : 
        time.sleep(1)
        filename = str(int(time.time()))
        flv_path = download(save_path, filename, url)

        if flv_path is not None:
            print "下载成功"+flv_path
            count = count + 1



if __name__ == "__main__":
    reload(sys)
    sys.setdefaultencoding("utf8")
    if len(sys.argv) != 4:
        print "usage: python get_kg_flv.py flv_url resolution level"
        sys.exit(-1)

    process(sys.argv[1], sys.argv[2], int(sys.argv[3]))
