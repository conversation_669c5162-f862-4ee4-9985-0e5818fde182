# -*- coding: utf-8 -*-
import requests
import json
import os
import datetime
#import dateutil.tz
import shutil
import io
import csv

# ts_dir = os.path.abspath('.')+"/ts"
# mp4_dir = os.path.abspath('.')+"/mp4"
# if os.path.exists(ts_dir):
#     shutil.rmtree(ts_dir)
# if os.path.exists(mp4_dir):
#     shutil.rmtree(mp4_dir)
# os.mkdir(ts_dir)
# os.mkdir(mp4_dir)
# now = datetime.datetime.now(dateutil.tz.tzlocal())
# timestamp = now.strftime('%Y_%m_%d_%H_%M_%S')
# mp4_name = timestamp + '.mp4'
# mp4_file = os.path.join(mp4_dir,mp4_name)

class QualityGrade():
    def __init__(self):
        self.headers = {'Content-type': 'application/json'}
        self.requet_url = "http://vc.y.qq.com/cgi-bin/musicu.fcg"
        self.proxies = {'https': 'https://127.0.0.1:12639',
                        'http': 'http://127.0.0.1:12639'}

    def get_video(self, showlist):
        foo = {
            "comm":{
                "tmeAppID":"mlive",
                "gzip":"0"
            },
            "req":{
                "module":"mlive.show.MliveShowInfoSvr",
                "method":"GetBypassStreamInfo",
                "param":{
                    "showIDs": showlist
                }
            }
        }
        json_params = json.dumps(foo)
        requests.packages.urllib3.disable_warnings()  # 取消所有警告
        return requests.post(self.requet_url, data=json_params, headers=self.headers, proxies= self.proxies)

    # def get_mp4(self, m3u8):
    #     ts_list = []
    #     video_slice = requests.get(m3u8, proxies= self.proxies).content
    #     video_slice_list = video_slice.split('\n')
    #     for slice_index in video_slice_list:
    #         if slice_index.endswith(".ts"):
    #             ts_list.append(slice_index)
    #     print(ts_list)
    #     ts_reqs = list(map(lambda x: 'http://stream.mlive.qq.com/live/'+x, ts_list))
    #     for i in range(0, len(ts_reqs)):
    #         tscontent = requests.get(ts_reqs[i], proxies=self.proxies).content
    #         filename =  os.path.join(ts_dir, ts_list[i])
    #         with open(filename , "wb") as f:
    #             f.write(tscontent)
    #     tsfile_list = []
    #     filenames = os.listdir(ts_dir)
    #     for filename in filenames:
    #         filter_file = filename.split('.')
    #         if filter_file[1] == 'ts':
    #             tsfile_list.append(os.path.join(ts_dir,filename))
    #     if len(tsfile_list) > 0:
    #         input_file = '|'.join(tsfile_list)
    #         output_file = mp4_file
    #         if not os.path.exists(output_file):
    #             command = 'ffmpeg -i "concat:%s" -acodec copy -vcodec copy -absf aac_adtstoasc %s' % (
    #             input_file, output_file)
    #     os.popen(command)

    def get_score(self, mp4_file):
        """
        向指定评分服务端接口上传视频文件并获取评分

        参数:
        mp4_file -- 要上传的MP4文件的路径

        返回值:
        response -- 服务器返回的响应对象
        """
        # 以二进制模式打开视频文件作为要上传的文件内容
        with open(mp4_file, 'rb') as video_file:
            files = {'profile': video_file}
            # 发送POST请求到评分服务器，携带文件和使用代理配置
            response = requests.post(
                url="http://10.101.136.240:8081/uploadAnchorVideo/",
                files=files,
                proxies=self.proxies
            )
        return response

if __name__ == "__main__":
    qualitygrade = QualityGrade()
    video_path = r"/Users/<USER>/Desktop/kg_video" \
                 r"" \
                 r"/"
    fb = open("result.txt", "wb+",buffering=0)
    for root, dirs, files in os.walk(video_path):
        for fl in files:
            #if fl.endswith(".mp4"):
            fl_path = os.path.join(root,fl)
            score_result = qualitygrade.get_score(fl_path)
            print(score_result.content)
            fb.write(str(score_result.content) + "\n")
            #else:
                #print "当前只支持mp4格式"
    fb.close()


