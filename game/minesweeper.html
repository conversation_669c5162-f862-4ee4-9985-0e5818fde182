<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫雷游戏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #1a1c2c 0%, #2a3042 100%);
        }
        
        .cell {
            width: var(--cell-size);
            height: var(--cell-size);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .cell:active {
            transform: scale(0.95);
        }
        
        .cell-revealed {
            background: linear-gradient(145deg, #2a3042, #232838);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .mine-count {
            user-select: none;
        }
        
        .cell-highlight {
            background-color: rgba(255, 255, 255, 0.15) !important;
            transform: scale(0.95);
            box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.3);
        }
        
        .cell-active {
            background-color: rgba(255, 255, 255, 0.25) !important;
            transform: scale(0.92);
            box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.4);
        }
        
        .game-container {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.05);
        }
        
        .difficulty-btn {
            transition: all 0.3s ease;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        
        .difficulty-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .stats-panel {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(5px);
        }
        
        /* 移除之前的滚动相关样式，添加新的布局样式 */
        .game-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        .game-container {
            width: fit-content;
            max-width: 95vw;
            margin: 0 auto;
        }

        .game-grid-container {
            display: flex;
            justify-content: center;
            width: 100%;
        }

        /* 不同难度下的网格大小调整 */
        .grid-beginner {
            --cell-size: 40px;
        }

        .grid-intermediate {
            --cell-size: 32px;
        }

        .grid-expert {
            --cell-size: 28px;
        }

        @media (min-width: 1280px) {
            .grid-intermediate {
                --cell-size: 35px;
            }
            .grid-expert {
                --cell-size: 30px;
            }
        }

        @media (max-width: 768px) {
            .grid-beginner {
                --cell-size: 35px;
            }
            .grid-intermediate {
                --cell-size: 28px;
            }
            .grid-expert {
                --cell-size: 24px;
            }
        }

        .revealed-number {
            transition: all 0.15s ease-out;
        }

        .revealed-number:hover {
            transform: scale(1.05);
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
        }

        .revealed-number:active {
            transform: scale(0.95);
        }

        /* 添加表情图标样式 */
        .game-emoji {
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", sans-serif;
            font-style: normal;
            font-size: 1.8rem;
            line-height: 1;
            display: inline-block;
            user-select: none;
        }

        /* 确保表情符号居中对齐 */
        #reset-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            padding: 0;
            border-radius: 9999px;
            transition: all 0.2s ease;
            background: rgba(255, 255, 255, 0.1);
        }

        #reset-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        #reset-btn:active {
            transform: scale(0.95);
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <!-- 添加返回按钮 -->
    <a href="index.html" 
       class="fixed top-4 left-4 px-4 py-2 rounded-xl 
              bg-gray-800/50 backdrop-blur text-white 
              hover:bg-gray-700/50 transition-all duration-300
              flex items-center gap-2 group">
        <span class="transform transition-transform group-hover:-translate-x-1">←</span>
        返回首页
    </a>

    <div class="game-wrapper bg-gradient-to-br from-gray-900 to-gray-800">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white text-shadow">扫雷游戏</h1>
        </div>
        
        <div class="game-container rounded-2xl p-6 shadow-2xl backdrop-blur-sm bg-white/5">
            <!-- 难度选择 -->
            <div class="mb-6 flex justify-center gap-4">
                <button class="difficulty-btn px-6 py-3 rounded-xl bg-blue-600 text-white hover:bg-blue-500 transition-all font-medium" 
                        data-difficulty="beginner">
                    初级
                </button>
                <button class="difficulty-btn px-6 py-3 rounded-xl bg-gray-700 text-white hover:bg-gray-600 transition-all font-medium" 
                        data-difficulty="intermediate">
                    中级
                </button>
                <button class="difficulty-btn px-6 py-3 rounded-xl bg-gray-700 text-white hover:bg-gray-600 transition-all font-medium" 
                        data-difficulty="expert">
                    高级
                </button>
            </div>

            <!-- 游戏状态 -->
            <div class="stats-panel mb-6 flex items-center justify-between p-4 rounded-xl">
                <div class="text-yellow-400 flex items-center gap-2">
                    <span class="game-emoji">💣</span>
                    <span id="mine-count" class="mine-count font-medium text-xl">10</span>
                </div>
                <button id="reset-btn" class="game-emoji hover:scale-110 transition-transform">🙂</button>
                <div class="text-yellow-400 flex items-center gap-2">
                    <span class="game-emoji">⏱️</span>
                    <span id="timer" class="mine-count font-medium text-xl">0</span>
                </div>
            </div>

            <!-- 游戏网格 -->
            <div class="game-grid-container rounded-xl bg-gray-800/50 backdrop-blur shadow-inner p-4">
                <div id="grid" class="inline-grid gap-1.5"></div>
            </div>

            <!-- 最佳记录 -->
            <div class="mt-6 stats-panel p-4 rounded-xl text-white">
                <h3 class="font-bold mb-4 text-lg text-yellow-400">最佳记录</h3>
                <div class="grid grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-gray-300 mb-1">初级</div>
                        <div id="best-beginner" class="font-medium">-</div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-300 mb-1">中级</div>
                        <div id="best-intermediate" class="font-medium">-</div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-300 mb-1">高级</div>
                        <div id="best-expert" class="font-medium">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新最近游玩时间戳
const updateRecentGame = () => {
    const recentGames = JSON.parse(localStorage.getItem('recentGames') || '[]');
    const gameId = location.pathname.includes('五子棋') ? 'gomoku' : 'minesweeper';
    
    const gameIndex = recentGames.findIndex(game => game.id === gameId);
    if (gameIndex !== -1) {
        recentGames[gameIndex].timestamp = Date.now();
        localStorage.setItem('recentGames', JSON.stringify(recentGames));
    }
};
updateRecentGame();

        const DIFFICULTY_SETTINGS = {
            beginner: { width: 9, height: 9, mines: 10 },
            intermediate: { width: 16, height: 16, mines: 40 },
            expert: { width: 30, height: 16, mines: 99 }
        };

        class Minesweeper {
            constructor(difficulty = 'beginner') {
                this.emojis = {
                    normal: '🙂',
                    success: '😎',
                    dead: '😵',
                    mine: '💣',
                    flag: '🚩'
                };
                this.setDifficulty(difficulty);
                this.init();
                this.render();
                this.setupEventListeners();
                this.loadBestTimes();
                this.updateDifficultyButtons();
            }

            init() {
                this.grid = [];
                for (let y = 0; y < this.height; y++) {
                    this.grid[y] = [];
                    for (let x = 0; x < this.width; x++) {
                        this.grid[y][x] = {
                            isMine: false,
                            neighborMines: 0
                        };
                    }
                }
            }

            setDifficulty(difficulty) {
                const settings = DIFFICULTY_SETTINGS[difficulty];
                this.width = settings.width;
                this.height = settings.height;
                this.mines = settings.mines;
                this.difficulty = difficulty;
                this.revealed = new Set();
                this.flagged = new Set();
                this.gameOver = false;
                this.gameStarted = false;
                this.timer = 0;
                if (this.timerInterval) clearInterval(this.timerInterval);

                document.getElementById('mine-count').textContent = this.mines;
                document.getElementById('timer').textContent = '0';
                document.getElementById('reset-btn').textContent = this.emojis.normal;

                const gridContainer = document.querySelector('.game-grid-container');
                gridContainer.className = `game-grid-container rounded-xl bg-gray-800/50 backdrop-blur shadow-inner p-4 grid-${difficulty}`;
            }

            render() {
                const grid = document.getElementById('grid');
                grid.style.gridTemplateColumns = `repeat(${this.width}, var(--cell-size))`;
                grid.innerHTML = '';

                for (let y = 0; y < this.height; y++) {
                    for (let x = 0; x < this.width; x++) {
                        const cell = document.createElement('div');
                        cell.className = `cell rounded-lg cursor-pointer select-none bg-gray-700/90 
                                        hover:bg-gray-600/90 font-bold`;
                        cell.dataset.x = x;
                        cell.dataset.y = y;
                        cell.dataset.index = y * this.width + x;
                        grid.appendChild(cell);
                    }
                }
            }

            ensureSafeStart(firstX, firstY) {
                // 清除现有的地雷
                this.init();

                // 放置地雷，避开首次点击的 3x3 区域
                let minesPlaced = 0;
                while (minesPlaced < this.mines) {
                    const x = Math.floor(Math.random() * this.width);
                    const y = Math.floor(Math.random() * this.height);
                    
                    // 检查是否在安全区域内
                    if (Math.abs(x - firstX) <= 1 && Math.abs(y - firstY) <= 1) {
                        continue;
                    }

                    if (!this.grid[y][x].isMine) {
                        this.grid[y][x].isMine = true;
                        minesPlaced++;
                    }
                }

                // 计算每个格子周围的地雷数
                for (let y = 0; y < this.height; y++) {
                    for (let x = 0; x < this.width; x++) {
                        if (!this.grid[y][x].isMine) {
                            this.grid[y][x].neighborMines = this.countAdjacentMines(x, y);
                        }
                    }
                }
            }

            countAdjacentMines(x, y) {
                let count = 0;
                for (let dy = -1; dy <= 1; dy++) {
                    for (let dx = -1; dx <= 1; dx++) {
                        if (dx === 0 && dy === 0) continue;
                        const newX = x + dx;
                        const newY = y + dy;
                        if (this.isValidCell(newX, newY) && this.grid[newY][newX].isMine) {
                            count++;
                        }
                    }
                }
                return count;
            }

            reveal(x, y) {
                if (this.gameOver || this.flagged.has(y * this.width + x)) return;

                // 首次点击
                if (!this.gameStarted) {
                    this.gameStarted = true;
                    this.ensureSafeStart(x, y);
                    this.startTimer();
                }

                const index = y * this.width + x;
                if (this.revealed.has(index)) return;

                const cell = this.getCell(x, y);
                this.revealed.add(index);

                if (this.grid[y][x].isMine) {
                    cell.textContent = this.emojis.mine;
                    cell.classList.add('bg-red-500', 'cell-revealed');
                    this.gameOver = true;
                    clearInterval(this.timerInterval);
                    document.getElementById('reset-btn').textContent = this.emojis.dead;
                    this.revealAll();
                    return;
                }

                cell.classList.remove('bg-gray-700/90', 'hover:bg-gray-600/90');
                cell.classList.add('bg-gray-600/70', 'cell-revealed');

                if (this.grid[y][x].neighborMines > 0) {
                    cell.textContent = this.grid[y][x].neighborMines;
                    cell.classList.add(this.getNumberColor(this.grid[y][x].neighborMines), 'revealed-number');
                } else {
                    this.revealEmptyNeighbors(x, y);
                }

                this.checkWin();
            }

            revealEmptyNeighbors(x, y) {
                for (let dy = -1; dy <= 1; dy++) {
                    for (let dx = -1; dx <= 1; dx++) {
                        if (dx === 0 && dy === 0) continue;
                        const newX = x + dx;
                        const newY = y + dy;
                        if (this.isValidCell(newX, newY)) {
                            const index = newY * this.width + newX;
                            if (!this.revealed.has(index) && !this.flagged.has(index)) {
                                this.reveal(newX, newY);
                            }
                        }
                    }
                }
            }

            toggleFlag(x, y) {
                if (this.gameOver || this.revealed.has(y * this.width + x)) return;

                const index = y * this.width + x;
                const cell = this.getCell(x, y);

                if (this.flagged.has(index)) {
                    this.flagged.delete(index);
                    cell.textContent = '';
                    document.getElementById('mine-count').textContent = 
                        parseInt(document.getElementById('mine-count').textContent) + 1;
                } else {
                    if (this.flagged.size >= this.mines) return;
                    this.flagged.add(index);
                    cell.textContent = this.emojis.flag;
                    document.getElementById('mine-count').textContent = 
                        parseInt(document.getElementById('mine-count').textContent) - 1;
                }
            }

            revealAll() {
                for (let y = 0; y < this.height; y++) {
                    for (let x = 0; x < this.width; x++) {
                        const index = y * this.width + x;
                        const cell = this.getCell(x, y);
                        
                        if (this.grid[y][x].isMine) {
                            if (!this.flagged.has(index)) {
                                cell.textContent = this.emojis.mine;
                                cell.classList.add('bg-red-500/80', 'cell-revealed');
                            }
                        }
                    }
                }
            }

            getCell(x, y) {
                return document.querySelector(`[data-x="${x}"][data-y="${y}"]`);
            }

            getCellCoords(index) {
                return {
                    x: index % this.width,
                    y: Math.floor(index / this.width)
                };
            }

            startTimer() {
                this.timerInterval = setInterval(() => {
                    this.timer++;
                    document.getElementById('timer').textContent = this.timer;
                }, 1000);
            }

            getNumberColor(number) {
                const colors = [
                    '',
                    'text-blue-400',
                    'text-green-400',
                    'text-red-400',
                    'text-blue-800',
                    'text-red-800',
                    'text-cyan-400',
                    'text-gray-800',
                    'text-gray-600'
                ];
                return colors[number];
            }

            checkWin() {
                const totalCells = this.width * this.height;
                const revealedCount = this.revealed.size;
                const correctFlags = Array.from(this.flagged).every(index => {
                    const {x, y} = this.getCellCoords(index);
                    return this.grid[y][x].isMine;
                });

                if (revealedCount + this.mines === totalCells && correctFlags) {
                    this.gameOver = true;
                    clearInterval(this.timerInterval);
                    document.getElementById('reset-btn').textContent = this.emojis.success;
                    this.saveBestTime();
                }
            }

            loadBestTimes() {
                const labels = {
                    'beginner': '初级',
                    'intermediate': '中级',
                    'expert': '高级'
                };
                
                Object.entries(labels).forEach(([diff, label]) => {
                    const bestTime = localStorage.getItem(`best-${diff}`);
                    document.getElementById(`best-${diff}`).textContent = 
                        bestTime ? `${bestTime} 秒` : '-';
                });
            }

            updateDifficultyButtons() {
                document.querySelectorAll('.difficulty-btn').forEach(btn => {
                    btn.classList.remove('bg-blue-600', 'hover:bg-blue-500');
                    btn.classList.add('bg-gray-700', 'hover:bg-gray-600');
                    
                    if (btn.dataset.difficulty === this.difficulty) {
                        btn.classList.remove('bg-gray-700', 'hover:bg-gray-600');
                        btn.classList.add('bg-blue-600', 'hover:bg-blue-500');
                    }
                });
            }

            setupEventListeners() {
                const grid = document.getElementById('grid');
                
                grid.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('cell')) return;
                    const x = parseInt(e.target.dataset.x);
                    const y = parseInt(e.target.dataset.y);
                    this.reveal(x, y);
                });

                grid.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    if (!e.target.classList.contains('cell')) return;
                    const x = parseInt(e.target.dataset.x);
                    const y = parseInt(e.target.dataset.y);
                    this.toggleFlag(x, y);
                });

                grid.addEventListener('mouseover', (e) => {
                    if (!e.target.classList.contains('cell')) return;
                    const x = parseInt(e.target.dataset.x);
                    const y = parseInt(e.target.dataset.y);
                    this.highlightNeighbors(x, y);
                });

                grid.addEventListener('mouseout', () => {
                    this.clearAllHighlights();
                });

                grid.addEventListener('mousedown', (e) => {
                    if (!e.target.classList.contains('cell')) return;
                    const x = parseInt(e.target.dataset.x);
                    const y = parseInt(e.target.dataset.y);
                    this.highlightNeighbors(x, y, true);
                });

                grid.addEventListener('mouseup', (e) => {
                    if (!e.target.classList.contains('cell')) return;
                    const x = parseInt(e.target.dataset.x);
                    const y = parseInt(e.target.dataset.y);
                    this.highlightNeighbors(x, y, false);
                });

                document.getElementById('reset-btn').addEventListener('click', () => {
                    this.setDifficulty(this.difficulty);
                    this.init();
                    this.render();
                });

                document.querySelectorAll('.difficulty-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        this.setDifficulty(btn.dataset.difficulty);
                        this.init();
                        this.render();
                        this.updateDifficultyButtons();
                    });
                });
            }

            highlightNeighbors(x, y, isActive = false) {
                if (!this.isValidCell(x, y)) return;
                
                const index = y * this.width + x;
                if (!this.revealed.has(index)) return;

                // 获取周围的未揭示格子
                const neighbors = this.getUnrevealedNeighbors(x, y);
                
                // 移除所有高亮
                this.clearAllHighlights();

                // 如果是已揭示的数字格子，高亮未揭示的邻居
                if (this.grid[y][x].neighborMines > 0) {
                    neighbors.forEach(({x: nx, y: ny}) => {
                        const cell = this.getCell(nx, ny);
                        if (cell && !this.flagged.has(ny * this.width + nx)) {
                            cell.classList.add(isActive ? 'cell-active' : 'cell-highlight');
                        }
                    });

                    // 高亮数字格子
                    const numberCell = this.getCell(x, y);
                    numberCell.style.transform = isActive ? 'scale(0.9)' : 'scale(1.05)';
                }
            }

            getUnrevealedNeighbors(x, y) {
                const neighbors = [];
                for (let dy = -1; dy <= 1; dy++) {
                    for (let dx = -1; dx <= 1; dx++) {
                        if (dx === 0 && dy === 0) continue;
                        const newX = x + dx;
                        const newY = y + dy;
                        if (this.isValidCell(newX, newY)) {
                            const index = newY * this.width + newX;
                            if (!this.revealed.has(index)) {
                                neighbors.push({x: newX, y: newY});
                            }
                        }
                    }
                }
                return neighbors;
            }

            isValidCell(x, y) {
                return x >= 0 && x < this.width && y >= 0 && y < this.height;
            }

            clearAllHighlights() {
                document.querySelectorAll('.cell').forEach(cell => {
                    cell.classList.remove('cell-highlight', 'cell-active');
                    cell.style.transform = '';
                });
            }

            saveBestTime() {
                const currentBest = localStorage.getItem(`best-${this.difficulty}`);
                if (!currentBest || this.timer < parseInt(currentBest)) {
                    localStorage.setItem(`best-${this.difficulty}`, this.timer.toString());
                    this.loadBestTimes();
                }
            }
        }

        // Start game
        new Minesweeper('beginner');
    </script>
</body>
</html>