# coding:utf-8
import os
import ndjson
import json
import pandas as pd
import json

def getdata_fromndjson(filename):
    """
    从ndjson文件中读取数据并转换为Excel文件

    参数:
    filename (str): 输入的ndjson文件名

    功能:
    1. 打开ndjson文件并解析每行JSON数据
    2. 将数据转换为pandas dataframe
    3. 将dataframe导出为Excel文件
    """
    # 使用map函数逐行加载JSON数据
    records = map(json.loads, open(filename))

    # 将生成器转换为DataFrame
    df = pd.DataFrame.from_records(records)

    # 将数据写入Excel文件（关闭索引列）
    df.to_excel('output5.xlsx', index=False)

    

if __name__ == '__main__':
    filename = r"/Users/<USER>/Desktop/6aa28d13-b81b-45ca-af6e-12669b6b7fea.ndjson"
    #getdata_fromndjson(filename)

    uin = []
    with open(r"/Users/<USER>/Desktop/uin_uv.txt", "r") as f:
        for line in f.readlines():
            if line.strip() not in uin:
                uin.append(line.strip())
    print(len(uin))





