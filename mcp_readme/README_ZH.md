# MCP 服务器图表  ![](https://badge.mcpx.dev?type=server 'MCP 服务器')  [![构建状态](https://github.com/antvis/mcp-server-chart/actions/workflows/build.yml/badge.svg)](https://github.com/antvis/mcp-server-chart/actions/workflows/build.yml) [![npm 版本](https://img.shields.io/npm/v/@antv/mcp-server-chart.svg)](https://www.npmjs.com/package/@antv/mcp-server-chart) [![smithery 徽章](https://smithery.ai/badge/@antvis/mcp-server-chart)](https://smithery.ai/server/@antvis/mcp-server-chart) [![npm 许可证](https://img.shields.io/npm/l/@antv/mcp-server-chart.svg)](https://www.npmjs.com/package/@antv/mcp-server-chart)

一个使用 [AntV](https://github.com/antvis/) 生成图表的模型上下文协议（MCP）服务器。我们可以使用这个 MCP 服务器进行 _图表生成_ 和 _数据分析_。

<a href="https://www.star-history.com/#antvis/mcp-server-chart&Date">
  <img width="512" src="https://api.star-history.com/svg?repos=antvis/mcp-server-chart&type=Date" />
</a>

这是一个基于 TypeScript 的 MCP 服务器，提供图表生成功能。它允许您通过 MCP 工具创建各种类型的图表。您也可以在 [Dify](https://marketplace.dify.ai/plugins/antv/visualization) 中使用它。

## 📋 目录

- [✨ 功能](#-功能)
- [🤖 使用方式](#-使用方式)
- [🚰 使用 SSE 或 Streamable 传输运行](#-使用-sse-或-streamable-传输运行)
- [🎮 CLI 选项](#-cli-选项)
- [⚙️ 环境变量](#%EF%B8%8F-环境变量)
  - [VIS_REQUEST_SERVER](#-私有部署)
  - [SERVICE_ID](#%EF%B8%8F-生成记录)
  - [DISABLED_TOOLS](#%EF%B8%8F-工具过滤)
- [📠 私有部署](#-私有部署)
- [🗺️ 生成记录](#%EF%B8%8F-生成记录)
- [🎛️ 工具过滤](#%EF%B8%8F-工具过滤)
- [🔨 开发](#-开发)
- [📄 许可证](#-许可证)

## ✨ 功能

目前支持 25+ 种图表。

<img width="768" alt="mcp-server-chart 预览" src="https://mdn.alipayobjects.com/huamei_qa8qxu/afts/img/A*IyIRQIQHyKYAAAAAgCAAAAgAemJ7AQ/fmt.avif" />

1. `generate_area_chart`: 生成 `面积图`，用于展示连续自变量下的数据趋势，便于观察整体数据变化。
1. `generate_bar_chart`: 生成 `条形图`，用于比较不同类别的数值，适合横向比较。
1. `generate_boxplot_chart`: 生成 `箱线图`，用于展示数据分布，包括中位数、四分位数和异常值。
1. `generate_column_chart`: 生成 `柱状图`，用于比较不同类别的数值，适合纵向比较。
1. `generate_district_map` - 生成 `行政区划地图`，用于展示行政分区和数据分布。
1. `generate_dual_axes_chart`: 生成 `双轴图`，用于展示两个不同单位或范围的变量之间的关系。
1. `generate_fishbone_diagram`: 生成 `鱼骨图`（又称石川图），用于识别和展示问题的根本原因。
1. `generate_flow_diagram`: 生成 `流程图`，用于展示流程的步骤和顺序。
1. `generate_funnel_chart`: 生成 `漏斗图`，用于展示不同阶段的数据损失。
1. `generate_histogram_chart`: 生成 `直方图`，通过将数据划分为区间并计算每个区间的数据点数来展示数据分布。
1. `generate_line_chart`: 生成 `折线图`，用于展示随时间或其他连续变量变化的数据趋势。
1. `generate_liquid_chart`: 生成 `水球图`，用于展示数据比例，以水球形式直观表示百分比。
1. `generate_mind_map`: 生成 `思维导图`，用于展示思维过程和层次信息。
1. `generate_network_graph`: 生成 `网络图`，用于展示节点之间的关系和连接。
1. `generate_organization_chart`: 生成 `组织结构图`，用于展示组织结构和人员关系。
1. `generate_path_map` - 生成 `路径地图`，用于展示 POI 的路线规划结果。
1. `generate_pie_chart`: 生成 `饼图`，用于展示数据比例，将数据划分为扇形部分显示各部分的百分比。
1. `generate_pin_map` - 生成 `标记地图`，用于展示 POI 的分布。
1. `generate_radar_chart`: 生成 `雷达图`，用于全面展示多维数据，以雷达形式显示多个维度。
1. `generate_sankey_chart`: 生成 `桑基图`，用于展示数据流动和流量，以桑基风格表示不同节点之间的数据移动。
1. `generate_scatter_chart`: 生成 `散点图`，用于展示两个变量之间的关系，在坐标系中以散点形式显示数据点。
1. `generate_treemap_chart`: 生成 `矩形树图`，用于展示层次数据，以矩形形式显示数据，矩形大小代表数据值。
1. `generate_venn_chart`: 生成 `韦恩图`，用于展示集合之间的关系，包括交集、并集和差集。
1. `generate_violin_chart`: 生成 `小提琴图`，用于展示数据分布，结合箱线图和密度图的特征，提供更详细的数据分布视图。
1. `generate_word_cloud_chart`: 生成 `词云图`，用于展示文本数据中词语的频率，字体大小表示每个词的频率。

> [!注意]
> 上述地理可视化图表生成工具使用 [高德地图服务](https://lbs.amap.com/)，目前仅支持中国境内的地图生成。

## 🤖 使用方式

在 `桌面应用程序` 中使用，如 Claude、VSCode、[Cline](https://cline.bot/mcp-marketplace)、Cherry Studio、Cursor 等，添加以下 MCP 服务器配置。在 Mac 系统上：

```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": [
        "-y",
        "@antv/mcp-server-chart"
      ]
    }
  }
}
```

在 Windows 系统上：

```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@antv/mcp-server-chart"
      ]
    }
  }
}
```

您也可以在 [阿里云](https://bailian.console.aliyun.com/?tab=mcp#/mcp-market/detail/antv-visualization-chart)、[ModelScope](https://www.modelscope.cn/mcp/servers/@antvis/mcp-server-chart)、[glama.ai](https://glama.ai/mcp/servers/@antvis/mcp-server-chart)、[smithery.ai](https://smithery.ai/server/@antvis/mcp-server-chart) 或其他支持 HTTP、SSE 协议的平台上使用。

## 🚰 使用 SSE 或 Streamable 传输运行

### 直接运行

全局安装软件包。

```bash
npm install -g @antv/mcp-server-chart
```

使用您首选的传输选项运行服务器：

```bash
# 使用 SSE 传输（默认端点：/sse）
mcp-server-chart --transport sse

# 使用自定义端点的 Streamable 传输
mcp-server-chart --transport streamable
```

然后您可以通过以下地址访问服务器：

- SSE 传输：`http://localhost:1122/sse`
- Streamable 传输：`http://localhost:1122/mcp`

### Docker 部署

进入 docker 目录。

```bash
cd docker
```

使用 docker-compose 部署。

```bash
docker compose up -d
```

然后您可以通过以下地址访问服务器：

- SSE 传输：`http://localhost:1123/sse`
- Streamable 传输：`http://localhost:1122/mcp`

## 🎮 CLI 选项

运行 MCP 服务器时可以使用以下 CLI 选项。通过 `-h` 参数运行 CLI 查看命令选项。

```plain
MCP 服务器图表 CLI

选项：
  --transport, -t  指定传输协议："stdio"、"sse" 或 "streamable"（默认："stdio"）
  --port, -p       为 SSE 或 streamable 传输指定端口（默认：1122）
  --endpoint, -e   为传输指定端点：
                   - 对于 SSE：默认为 "/sse"
                   - 对于 streamable：默认为 "/mcp"
  --help, -h       显示帮助信息
```

## ⚙️ 环境变量

| 变量 | 描述 | 默认值 | 示例 |
|----------|:------------|---------|---------|
| `VIS_REQUEST_SERVER` | 用于私有部署的自定义图表生成服务 URL | `https://antv-studio.alipay.com/api/gpt-vis` | `https://your-server.com/api/chart` |
| `SERVICE_ID` | 用于图表生成记录的服务标识符 | - | `your-service-id-123` |
| `DISABLED_TOOLS` | 要禁用的工具名称的逗号分隔列表 | - | `generate_fishbone_diagram,generate_mind_map` |


### 📠 私有部署

`MCP 服务器图表` 默认提供免费的图表生成服务。对于需要私有部署的用户，可以尝试使用 `VIS_REQUEST_SERVER` 来自定义自己的图表生成服务。

```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": [
        "-y",
        "@antv/mcp-server-chart"
      ],
      "env": {
        "VIS_REQUEST_SERVER": "<您的图表生成服务URL>"
      }
    }
  }
}
```

您可以使用 AntV 的项目 [GPT-Vis-SSR](https://github.com/antvis/GPT-Vis/tree/main/bindings/gpt-vis-ssr) 在私有环境中部署 HTTP 服务，然后通过 env `VIS_REQUEST_SERVER` 传递 URL 地址。

- **方法**: `POST`
- **参数**: 将传递给 `GPT-Vis-SSR` 进行渲染。例如：`{ "type": "line", "data": [{ "time": "2025-05", "value": 512 }, { "time": "2025-06", "value": 1024 }] }`。
- **返回**: HTTP 服务的返回对象。
  - **success**: `boolean` 是否成功生成图表图像。
  - **resultObj**: `string` 图表图像 URL。
  - **errorMessage**: `string` 当 `success = false` 时，返回错误信息。

> [!注意]
> 私有部署解决方案目前不支持地理可视化图表生成，包括 3 个工具：`geographic-district-map`、`geographic-path-map`、`geographic-pin-map`。

### 🗺️ 生成记录

默认情况下，用户需要自行保存结果，但我们也提供了查看图表生成记录的服务，这需要用户生成自己的服务标识符并进行配置。

使用支付宝扫码打开小程序生成个人服务标识符（点击下方"我的"菜单，进入"我的服务"页面，点击"生成"按钮，成功后点击"复制"按钮）：

<img alt="我的服务标识网站" width="240" src="https://mdn.alipayobjects.com/huamei_dxq8v0/afts/img/dASoTLt6EywAAAAARqAAAAgADu43AQFr/fmt.webp" />

接下来，您需要将 `SERVICE_ID` 环境变量添加到 MCP 服务器配置中。例如，Mac 配置如下（Windows 系统只需添加 `env` 变量）：

```json
{
  "mcpServers": {
    "AntV Map": {
      "command": "npx",
      "args": [
        "-y",
        "@antv/mcp-server-chart"
      ],
      "env": {
        "SERVICE_ID": "***********************************"
      }
    }
  }
}
```

更新 MCP 服务器配置后，您需要重启 AI 客户端应用程序，并检查是否已成功启动并连接到 MCP 服务器。然后您可以尝试再次生成地图。生成成功后，您可以进入小程序的"我的地图"页面查看您的地图生成记录。

<img alt="我的地图记录网站" width="240" src="https://mdn.alipayobjects.com/huamei_dxq8v0/afts/img/RacFR7emR3QAAAAAUkAAAAgADu43AQFr/original" />

### 🎛️ 工具过滤

您可以使用 `DISABLED_TOOLS` 环境变量禁用特定的图表生成工具。当某些工具与您的 MCP 客户端存在兼容性问题，或者您想限制可用功能时，这非常有用。

```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": [
        "-y",
        "@antv/mcp-server-chart"
      ],
      "env": {
        "DISABLED_TOOLS": "generate_fishbone_diagram,generate_mind_map"
      }
    }
  }
}
```

**可用于过滤的工具名称** 请参见 [✨ 功能](#-功能)。

## 🔨 开发

安装依赖：

```bash
npm install
```

构建服务器：

```bash
npm run build
```

启动 MCP 服务器：

```bash
npm run start
```

## 📄 许可证

MIT@[AntV](https://github.com/antvis)。